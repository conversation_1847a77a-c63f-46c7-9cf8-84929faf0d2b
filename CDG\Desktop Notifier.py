import requests
import xml.etree.ElementTree as ET
import time
from win10toast import ToastNotifier

# URL of BBC News RSS feed
RSS_FEED_URL = "http://feeds.bbci.co.uk/news/rss.xml"


def loadRSS():
    """
    Utility function to load RSS feed
    """
    # Create HTTP request response object
    resp = requests.get(RSS_FEED_URL)

    # Return response content
    return resp.content


def parseXML(rss):
    """
    Utility function to parse XML format RSS feed
    """
    # Create element tree root object
    root = ET.fromstring(rss)

    # Create empty list for news items
    newsitems = []

    # Iterate news items
    for item in root.findall("./channel/item"):
        news = {}
        # Iterate child elements of item
        for child in item:
            # Store tag and text for each child (title, description, link, etc.)
            news[child.tag] = child.text
        newsitems.append(news)

    # Return news items list
    return newsitems


def topStories():
    """
    Main function to generate and return news items
    """
    # Load RSS feed
    rss = loadRSS()

    # Parse XML
    newsitems = parseXML(rss)
    return newsitems


def showDesktopNotification():
    """
    Function to display desktop notifications for top news
    """
    # Fetch top news stories
    newsitems = topStories()

    # Create an instance of ToastNotifier
    toaster = ToastNotifier()

    # Display each news item as a desktop notification
    for newsitem in newsitems:
        title = newsitem.get("title", "No Title")
        description = newsitem.get("description", "No Description")

        # Show notification
        toaster.show_toast(title, description, duration=10)

        # Short delay between notifications
        time.sleep(2)


if __name__ == "__main__":
    showDesktopNotification()
