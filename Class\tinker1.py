from tkinter import *
from tkinter import font
import random


def helo():
    print("hi")


def show(name):
    print(f"course: {name}")


def random_color():
    r = lambda: random.randint(0, 255)
    return "#%02X%02X%02X" % (r(), r(), r())


root = Tk()
scrW = root.winfo_screenwidth()
srcH = root.winfo_screenheight()
print(scrW, srcH)
root.geometry("800x600+%d+%d" % (scrW / 2 - 400, srcH / 2 - 300))
root.title("Test1")
# root.iconbitmap("icon.ico")
root["bg"] = "green"
root.minsize(500, 200)
print(font.families())
# label1 = Label(
#     root,
#     text="left",
#     bg="yellow",
#     width=10,
#     padx=20,
#     pady=10,
#     font=("Times 25"),
# ).pack(side="left", anchor="nw", ipadx=50)
# label1 = Label(
#     root,
#     text="right",
#     bg="red",
#     width=10,
#     padx=20,
#     pady=10,
#     font=("Old English Text MT", 15),
# ).pack(side="right", anchor="se")
# label2 = Label(
#     root, text="mid", bg="blue", width=10, padx=20, pady=10, font=("Niagara Solid", 15)
# ).pack(side="top", fill="y", expand=True)
# # ===================================================================
label1 = Label(
    root,
    text="left",
    bg="yellow",
    width=10,
    padx=0,
    pady=50,
    font=("Arial 20 italic"),
    anchor="w",
).place(relx=0.2, rely=0.1)
label1 = Label(
    root,
    text="wtf",
    bg="red",
    width=10,
    padx=20,
    pady=10,
    font=("Times 20 underline"),
    borderwidth=10,
    relief="solid",
).place(width=20, height=10, relheight=0.1, relwidth=0.1)
# label1 = Label(
#     root,
#     text="left",
#     bg="yellow",
#     width=10,
#     padx=20,
#     pady=10,
#     font=("Old Englkish Text MT", 15),
# ).place(relx=0, rely=0.5, anchor="center")
# ========================================================================================

# label1 = Label(
#     root,
#     text="left",
#     bg="yellow",
#     width=10,
#     padx=20,
#     pady=10,
#     font=("Old English Text MT", 15),
# ).grid(row=0, column=0, sticky="nw")
# label1 = Label(
#     root, text="right", bg="red", width=10, padx=20, pady=10, font=("Rage Italic", 15)
# ).grid(row=0, column=1, sticky="s")
# label1 = Label(
#     root, text="test 1", bg="red", width=10, padx=20, pady=10, font=("Rage Italic", 15)
# ).grid(row=0, column=2, sticky="ne")
# root.columnconfigure(0, weight=1)  # chia deu 3 cot
# root.columnconfigure(1, weight=1)
# root.columnconfigure(2, weight=1)
# root.rowconfigure(0, weight=1)  # chinh sua 1 dong


# btnhelo = Button(
#     root,
#     text="helo",
#     bg="yellow",
#     fg="red",
#     padx=10,
#     pady=10,
#     command=helo,
#     activebackground="brown",
#     activeforeground="pink",
# )
# btnhelo.grid(row=0, column=0)
# btpy = Button(
#     root,
#     text="py",
#     bg="yellow",
#     fg="red",
#     padx=10,
#     pady=10,
#     command=lambda: show("python"),
#     activebackground="brown",
#     activeforeground="pink",
# )
# btpy.grid(row=0, column=1)
# bttinker = Button(
#     root,
#     text="tinter",
#     bg="yellow",
#     fg="red",
#     padx=10,
#     pady=10,
#     command=lambda: show("tkinter"),  # co tham so can them lamda
#     activebackground="brown",
#     activeforeground="pink",
# )
# bttinker.grid(row=0, column=2)
# bttinker = Button(
#     root,
#     text="tinter",
#     bg="yellow",
#     fg="red",
#     padx=10,
#     pady=10,
#     width=50,
#     command=root.destroy,
# )
# bttinker.grid(row=1, column=0, columnspan=3, sticky="wse")
# root.rowconfigure(0, weight=1)
# root.columnconfigure(0, weight=1)
# root.columnconfigure(1, weight=1)
# root.columnconfigure(2, weight=1)
# root.rowconfigure(0, weight=1)
# root.rowconfigure(1, weight=1)


root.mainloop()
