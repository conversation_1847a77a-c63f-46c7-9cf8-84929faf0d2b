from matplotlib import pyplot


def hinh_chung():
    x_data = [1, 2, 3, 4, 5]
    y_data = [5.5, 6.4, 5.3, 4.4, 7.9]

    pyplot.scatter(x_data, y_data)
    pyplot.savefig("def0.png")

    y_data = [1.4, 6.9, 8.8, 3.4, 4.4]

    pyplot.scatter(x_data, y_data)
    pyplot.savefig("def0.png")


def hinh_rieng():
    pass

    x_data = [1, 2, 3, 4, 5]
    y_data = [5.5, 6.4, 5.3, 4.4, 7.9]

    figure = pyplot.figure()
    pyplot.scatter(x_data, y_data)
    figure.savefig("def1.png")

    y_data = [1.4, 6.9, 8.8, 3.4, 4.4]

    figure = pyplot.figure()
    pyplot.scatter(x_data, y_data)
    figure.savefig("def2.png")
