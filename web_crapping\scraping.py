import requests
from bs4 import BeautifulSoup
import os
import time
import pandas as pd


def test():
    html_doc = """
<html>

<head>
    <title>The Dormouse's story</title>
</head>

<body>
    <p class="title"><b>The Dormouse's story</b></p>

    <p class="story">
        Once upon a time there were three little sisters; and their names were
        <a href="http://example.com/elsie" class="sister" id="link1"><PERSON></a>,
        <a href="http://example.com/lacie" class="sister" id="link2"><PERSON><PERSON></a> and
        <a href="http://example.com/tillie" class="sister" id="link3"><PERSON>ie</a>;
        and they lived at the bottom of a well.
    </p>

</body>

</html>
"""

    soup = BeautifulSoup(html_doc, "html.parser")
    print(soup)
    sisters = soup.select(".sister")

    for sister in sisters:
        print(sister.string)


def scrap_book():
    def get_rating(tag):
        print(tag["class"])
        for term, rating in rating_mappings.items():
            if term in tag["class"]:
                return rating

    rating_mappings = {
        "One": "★",
        "Two": "★ ★",
        "Three": "★ ★ ★",
        "Four": "★ ★ ★ ★",
        "Five": "★ ★ ★ ★ ★",
    }

    price_selector = ".price_color"
    title_selector = ".product_pod h3 a"
    rating_selector = ".star-rating"

    data = requests.get(
        "http://books.toscrape.com/"
    ).content  # text json() status_code headers
    # print(data)
    soup = BeautifulSoup(data, "html.parser")  # lxml html5lib

    prices = soup.select(price_selector)
    titles = soup.select(title_selector)
    ratings = soup.select(rating_selector)
    with open("books.csv", "w", encoding="utf-8") as book_file:
        for price, title, rating in zip(prices, titles, ratings):
            book_file.write(
                f"{title['title']},{price.string},{get_rating(rating)}\n")
            break


def test1():
    html = """<html>
    <!--  div>p+span-->
    <head>
        <title>page demo</title>
        <link rel="stylesheet" href="style.css" type="text/css" />
    </head>
    <body>
        <h1>head lv 1</h1>
        <p>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Ratione pariatur
        perspiciatis, eum possimus deserunt porro ducimus mollitia iste vel
        aliquid perferendis reiciendis omnis minima excepturi! Incidunt eum eaque
        nobis corporis.
        </p>
        <h2>head lv 2</h2>
        <ul>
        food
        <li>orange</li>
        <li>banana</li>
        <li>kiwi</li>
        <li>apple</li>
        </ul>
        <table border="1">
        <tr>
            <th>id</th>
            <th>ten</th>
            <th>tuoi</th>
        </tr>
        <tr>
            <td>1</td>
            <td>a</td>
            <td>12</td>
        </tr>
        <tr>
            <td>2</td>
            <td>b</td>
            <td>23</td>
        </tr>
        </table>
        <ol>
        things to do
        <li>eat</li>
        <li>sleep</li>
        <li>play</li>
        </ol>
        <img src="samac.jpg" alt="asd" width="200" />
        <p class="test1">
        Lorem ipsum dolor sit amet consectetur adipisicing elit. A, adipisci! Ad
        et esse enim illo cupiditate labore quasi illum nemo placeat? Deserunt
        aspernatur fugiat eveniet ipsum, debitis fugit obcaecati asperiores.
        </p>
        <a href="https://www.youtube.com/">youtube</a>
    </body>"""

    soup = BeautifulSoup(html, "html.parser")
    h1_tag = soup.find("h1")
    print(h1_tag.string)  # text: gộp phần tử con, in ra
    print(h1_tag.attrs)  # Thuộc tính của thẻ h1

    # li = soup.find_all("li")
    # print(type(li))
    # for i in li:
    #     print(i.string)

    # p_withclass = soup.find("p", attrs={"class": "test1"})
    # print(type(p_withclass))
    # print(p_withclass.string)

    # # tìm kiếm theo selector
    # p_withclass = soup.select_one("p.test1")
    # print(type(p_withclass))
    # p_text = p_withclass.string
    # print(p_text.title())  #


def scrap1():
    # count = 0
    URL = "https://books.toscrape.com/"
    # col-xs-6 col-sm-4 col-md-3 col-lg-3
    BOOK_PAGE = "ol.row li.col-xs-6.col-sm-4.col-md-3.col-lg-3"
    TITLE = "article.product_pod h3 a"
    PRICE = "div.product_price p.price_color"
    RATING = "article.product_pod p:nth-of-type(1)"
    IMG = "article.product_pod div.image_container a img"
    page = requests.get(URL)
    soup = BeautifulSoup(page.content, "html.parser")

    # for count, book in enumerate(soup.select(BOOK_PAGE), start=1):
    #     # count += 1
    #     # if count == 5:
    #     #     break
    #     book_title = book.select_one(TITLE).attrs["title"]
    #     book_price = book.select_one(PRICE).string
    #     book_rating = book.select_one(RATING).attrs["class"]
    #     book_rating = [x for x in book_rating if x != "star-rating"][0]
    #     img_link = URL + book.select_one(IMG).get("src")
    #     print(book_title, book_price, book_rating, img_link)
    #     content = requests.get(img_link).content
    #     # with open(f"book_{count}.png", "wb") as f:
    #     #     f.write(content)


def write_csv():
    start = time.time()
    list_link = [
        f"https://books.toscrape.com/catalogue/page-{i}.html" for i in range(1, 2)
    ]

    for url in list_link:
        BOOK_PAGE = "ol.row li.col-xs-6.col-sm-4.col-md-3.col-lg-3"
        TITLE = "article.product_pod h3 a"
        PRICE = "div.product_price p.price_color"
        RATING = "article.product_pod p:nth-of-type(1)"
        IMG = "article.product_pod div.image_container a img"
        page = requests.get(url)
        soup = BeautifulSoup(page.content, "html.parser")
        with open("data.csv", "w", encoding="utf-8") as f:
            f.write("title, price, rating, image_link\n")
            for count, book in enumerate(soup.select(BOOK_PAGE), start=1):
                book_title = book.select_one(TITLE).attrs["title"]
                book_price = book.select_one(PRICE).string
                book_rating = book.select_one(RATING).attrs["class"]
                book_rating = [x for x in book_rating if x != "star-rating"][0]
                img_link = url + book.select_one(IMG).get("src")

                f.write(
                    f'"{book_title}",{book_price},{book_rating},{img_link}\n')
    end = time.time()
    print(end - start)


write_csv()


def read_csv():
    import csv

    with open("data.csv", mode="r", encoding="utf-8") as file:
        reader = csv.DictReader(file)  # Đọc file với DictReader
        reader.fieldnames = [name.strip() for name in reader.fieldnames]
        print(reader.fieldnames)
        for row in reader:
            # In ra từng cột của mỗi dòng
            title = row["title"]
            price = row["price"]
            rating = row["rating"]
            image_link = row["image_link"]

            print(
                f"Title: {title}, Price: {price}, Rating: {rating}, Image Link: {image_link}"
            )

    import pandas as pd

    # Đọc file CSV vào DataFrame
    df = pd.read_csv("data.csv")

    # Xóa khoảng trắng trong tên cột
    df.columns = df.columns.str.strip()

    # Hiển thị 5 dòng đầu tiên trong DataFrame để kiểm tra
    print(df.head())

    # Thông tin tổng quan về DataFrame
    print(df.info())

    # Đếm số lượng sách theo rating
    print("\nCount of books by rating:")
    print(df["rating"].value_counts())
