from tkinter import *
import tkinter as tk
from PIL import Image, ImageTk
from tkinter import messagebox as mb
import random
from tkinter.ttk import *  # combobox
from tkinter import simpledialog  # IMPORT THƯ VIỆN SIMPLE DIALOG


def random_color():
    r = lambda: random.randint(0, 255)
    return "#%02X%02X%02X" % (r(), r(), r())


def a():
    name = inp1.get()
    name = Label(root, text=name)
    name.place(x=100, y=200)


def summ():
    a = float(inp1.get()) + float(inp2.get())
    name = Label(root, text="ket qua: " + str(a))
    name.place(x=100, y=200)


def clicked():
    name = Label(
        root,
        width=0,
        text="Bạn đã chọn: " + combo.get(),
        foreground="brown",
    )
    name.place(x=50, y=200)


root = Tk()
root.title("tinker2")
root.geometry("800x600+300+50")
root["bg"] = random_color()
# root.attributes("-topmost", True)  # window luon top
# name = Label(root, text="nhap so 1 ")
# name.place(x=30, y=30)
# name1 = Label(root, text="nhap so 2 ")
# name1.place(x=60, y=60)
# but = Button(root, text="click", width=10, command=summ)
# but.place(x=90, y=90)
# inp1 = Entry(root, width=10)
# inp1.insert(END, "nhap ho ten")  # gia tri mac dinh
# inp1.focus()  # luon tap trung vao entry
# inp1.pack()
# inp2 = Entry(root, width=10)
# inp2.pack()
# img = Image.open("fire.png")
# img = img.resize((30, 30), Image.BILINEAR)
# photo = ImageTk.PhotoImage(img)
# but = Button(root, text="click", image=photo)
# but.place(x=90, y=90)

# combo = Combobox(root, width=50,state="readonly")  # THIẾT LẬP HIỂN THỊ COMBO BOX
# combo["value"] = (
#     "Cà Rốt",
#     "Cải Thìa",
#     "Rau Quế",
#     "Cà Chua",
#     "Rau Răm",
# )  # LIST DANH SÁCH TRONG COMBO BOX
# combo.current(0) # hien thi vi tri hien tai
# combo.place(x=0, y=20)
# btn = Button(root, text="Lựa chọn", command=clicked)
# btn.place(x=0, y=100)

# root.withdraw()  # CHO CỬA SỔ WIN ẨN ĐI
nhap_lieu = simpledialog.askstring(
    title="Test", prompt="Bạn tên gì?:", initialvalue="khanh"
)
quit_button = tk.Button(
    root,
    text="click to quit",
    command=root.destroy,
    background="red",
    font="Times 20 underline",
)
quit_button.pack()
print("Xin chào", nhap_lieu)
root.mainloop()
