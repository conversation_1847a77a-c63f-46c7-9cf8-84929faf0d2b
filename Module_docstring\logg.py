import logging
import datetime

logging.basicConfig(
    level=logging.DEBUG,
    filename="log.log",
    format="%(asctime)s %(levelname)-8s: %(message)s line: %(lineno)d",
    datefmt="%d-%m-%Y %H:%M:%S",
    filemode="w",
)
# logger = logging.getLogger()

# logging.debug("asd")
logging.error("asd ")
logging.info("asd ")
logging.warning("asd ")

try:
    a = 5 / 0
except:
    logging.exception("Error occurred")
