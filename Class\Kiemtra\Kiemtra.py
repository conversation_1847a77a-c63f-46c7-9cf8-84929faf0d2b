import random
from datetime import datetime, timedelta, date
import json


def random_chucvu():
    ho = random.choice([chr(i) for i in range(ord("A"), ord("Z") + 1)])
    return ho


def random_name():
    ho = random.choice(["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"])
    tendem = random.choice(
        [
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>rong",
            "<PERSON>",
            "Tien",
            "<PERSON><PERSON>",
            "Tan",
            "Gia",
        ]
    )
    ten = random.choice(
        [
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>ian<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "Thanh",
        ]
    )

    return ho, tendem, ten


def random_day():
    start = date(1980, 1, 1)
    end = date(2004, 12, 31)
    delta = (end - start).days
    return start + timedelta(days=random.randint(1, delta))


def random_phongban():
    phong = random.choice(
        [
            "<PERSON> toan",
            "<PERSON><PERSON> su",
            "Marketing",
            "Kinh doanh",
            "Phat trien san pham",
            "Ky thuat",
            "Tai chinh",
            "Quan ly chat luong",
            "Hanh chinh",
            "Quan ly khach hang",
        ]
    )
    return phong


class NV:

    def __init__(self, hoten, ngaysinh, chucvu, phongban):
        self.hoten = hoten
        self.ngaysinh = ngaysinh
        self.chucvu = chucvu
        self.phongban = phongban

    def showNV(self):
        print(
            f"ho ten: {self.hoten} ngay sinh: {self.ngaysinh} chuc vu: {self.chucvu} phong ban: {self.phongban} "
        )

    def Create(DsPB, DsNV):
        try:
            ngaysinh = random_day()
            ho, dem, ten = random_name()
            hoten = f"{ho} {dem} {ten}".strip()
            chucvu = input("nhap chuc vu NV: ")
            if DsPB.danhsach == []:
                raise ValueError("danh sach phong ban trong")
            DsPB.showphongban()
            phongban = input("Nhập phòng ban theo danh sách trên: ")
            if phongban not in DsPB.danhsach:
                raise ValueError("Phòng ban không tồn tại trong danh sách.")
            nv = NV(hoten, ngaysinh, chucvu, phongban)  # khởi tạo 1 nhân viên a
            DsNV.themNV(nv)  # thêm nhân viên a vào danh sách nhân viên
        except ValueError as e:
            print(f"lỗi: {e} ")

    def Create_random(DsPB):
        ngaysinh = random_day()
        ho, dem, ten = random_name()
        hoten = f"{ho} {dem} {ten}".strip()
        return hoten, ngaysinh


class DanhsachNV:
    def __init__(self):
        self.danhsach = []

    def them12NVvao4PB(self, DsPB):
        try:
            if DsPB.danhsach == []:
                raise ValueError("danh sach phong ban trong")
            for i in DsPB.danhsach:
                for j in range(3):
                    a, b = NV.Create_random(phongban)
                    nv = NV(a, b, random_chucvu(), i)
                    self.themNV(nv)  # thêm nhân viên a vào danh sách nhân viên
        except ValueError as e:
            print(f"loi~ {e}:")

    def themNV(self, nhanvien):
        self.danhsach.append(nhanvien)

    def showDS(self):
        print("danh sach nhan vien: ")
        for i in self.danhsach:
            i.showNV()

    def Suathongtin(self, danhsachphong):
        try:
            if danhsachphong.danhsach == []:
                raise ValueError("danh sach phong ban trong")
            ten = input("nhap ho ten NV can sua: ")
            for i in self.danhsach:
                if ten not in [nv.hoten for nv in self.danhsach]:
                    raise ValueError("Nhân viên không tồn tại trong danh sách.")
                if i.hoten == ten:
                    ngaysinh = input("Nhập ngày sinh mới (dd/mm/yyyy) : ")
                    ngaysinh_new = datetime.strptime(
                        ngaysinh, "%d/%m/%Y"
                    )  # str -> date
                    chucvu_new = input("thay doi chuc vu: ")
                    danhsachphong.showphongban()
                    phongban_new = input("thay đổi phòng ban theo danh sách trên: ")
                    if phongban_new not in danhsachphong.danhsach:
                        raise ValueError("Phong ban mới khong co trong danh sach")
                    i.ngaysinh = ngaysinh_new
                    i.chucvu = chucvu_new
                    i.phongban = phongban_new
        except ValueError as e:
            print(f"loi~: {e}")

    def Xoa(self):
        flag = False
        ten = input("nhap ten NV can xóa: ")
        for i in self.danhsach:
            if i.hoten == ten:
                flag = True
                self.danhsach.remove(i)
        if not flag:
            print(f"không tìm thấy NV {ten}")

    def Timkiem(self):
        find = input("nhap ten NV can tim`: ")
        flag = True
        for nv in self.danhsach:
            if nv.hoten.endswith(find):
                print(f"Tim thay NV: {nv.hoten}")
                nv.showNV()
                flag = False
        if flag:
            print(f"Khong tim thay NV: {find}")

    def docfile(self, DsPB):
        with open("employee.json", "r", encoding="utf-8") as f:
            data = json.load(f)
            phongban_set = set()
            for i in data:
                hoten = i["hoten"]
                ngaysinh = i["ngaysinh"]
                chucvu = i["chucvu"]
                phongban = i["phongban"]
                nv = NV(hoten, ngaysinh, chucvu, phongban)
                self.danhsach.append(nv)
                if phongban not in phongban_set:
                    DsPB.danhsach.append(phongban)
                    phongban_set.add(phongban)
                nv.showNV()

    def ghifile(self):
        data = []
        for i in self.danhsach:
            if type(i.ngaysinh) is not str:
                ngaysinh_str = i.ngaysinh.strftime("%d-%m-%Y") #date -> str
                i.__dict__["ngaysinh"] = ngaysinh_str
            else:
                pass
            print(i.__dict__["ngaysinh"], type(i.__dict__["ngaysinh"]))
            # print(i,i.__dict__,type(i),type(i.__dict__))
            data.append(i.__dict__)
        for i in data:
            # print(data,type(data))
            print(i)
        with open("out.json", "w", encoding="utf-8") as file:
            # Ghi đối tượng JSON j
            json.dump(data, file, indent=4, ensure_ascii=False)  # indent : thut le


class Phongban:
    def __init__(self):
        self.danhsach = []

    def them4phongban(self):
        while len(set(self.danhsach)) < 4:
            ten = random_phongban()
            if ten not in self.danhsach:
                self.danhsach.append(ten)
                print(f"Thêm thành công phòng {ten}")

    def themphongban(self):
        ten = input("nhap ten phong ban")
        if ten not in self.danhsach:
            self.danhsach.append(ten)
            print(f"Thêm thành công phòng {ten}")
        # return self

    def showphongban(self):
        print("so luong phong ban: ", len(self.danhsach))
        print("danh sach phong ban: ", end=" ")
        for i in self.danhsach:
            print(i, end=", ")

    def capnhat(self, ten_cu, ten_moi, DsNV):
        if ten_cu in self.danhsach:
            for nv in DsNV.danhsach:
                if nv.phongban == ten_cu:
                    nv.phongban = ten_moi
            index = self.danhsach.index(ten_cu)
            self.danhsach[index] = ten_moi
            print(f"Phòng ban '{ten_cu}'cập nhật thành '{ten_moi}'.")
            # self.danhsach.remove(ten_cu)
        else:
            print(f"Không tìm thấy phòng ban '{ten_cu}'.")

    def xoaphongban(self, DsNV):
        try:
            phong = input("nhap phòng ban can xóa: ")
            if phong not in self.danhsach:
                raise ValueError("phòng ban không tồn tại trong danh sách.")
            self.danhsach.remove(phong)
            self.showphongban()
            ten_moi = input(
                "nhap ten phong ban moi co trong danh sach hiện tại để chuyển NV: "
            )
            if ten_moi not in self.danhsach:
                raise ValueError("phòng ban không tồn tại trong danh sách.")
            for nv in DsNV.danhsach:
                if nv.phongban == phong:
                    nv.phongban = ten_moi

            print(f"Phòng ban '{phong}' đã được xóa.")
        except ValueError as e:
            print(f"loi~: {e}")

    def hienthi_theophongban(self, danhsachnv):
        try:
            self.showphongban()
            phong = input("nhap phong ban can hien thi: ")
            if phong not in self.danhsach:
                raise ValueError("phòng ban không tồn tại trong danh sách.")
            for j in danhsachnv.danhsach:
                if j.phongban == phong:
                    j.showNV()
        except ValueError as e:
            print(f"loi~: {e}")

    def soluong_theophongban(self, danhsachnv):
        for phong in self.danhsach:
            count = 0
            for nv in danhsachnv.danhsach:
                if nv.phongban == phong:
                    count += 1
            print(f"Phòng: {phong} co {count} NV")

    def thongke_theophongban(self, danhsachnv):
        for phong in self.danhsach:
            print(f"Phòng: {phong} :")
            for nv in danhsachnv.danhsach:
                if nv.phongban == phong:
                    print(f"NV: {nv.hoten}")

    def ghifile_phongban(self):
        data = []
        for i in self.danhsach:
            data.append(i)
        with open("department.json", "w", encoding="utf-8") as file:
            # Ghi đối tượng JSON j
            json.dump(data, file, indent=4, ensure_ascii=False)  # indent : thut le


phongban = Phongban()  # khởi tạo danh sách phòng ban
danhsach = DanhsachNV()  # tạo danh sách nhân viên
while 1:

    print("=" * 70)
    print("1.them 1 nhan vien", end="| ")
    print("2.hien thi danh sach nhan vien", end="| ")
    print("3.Sua thong tin 1 nhan vien", end="| ")
    print("4.Xoa 1 nhan vien", end="| ")
    print("5.Tim kiem 1 nhan vien", end="| ")
    print("6.hien thi danh sach phong ban", end="| ")
    print("7.xoa 1 phong ban", end="| ")
    print("8.thay doi 1 phong ban", end="| ")
    print("9.hien thi phong ban theo ten", end="| ")
    print("10.them 1 phong ban ")
    print("11.thong ke nhan vien moi phong ban ", end="| ")
    print("12.so luong moi phong ban ", end="| ")
    print("13.ghi file NV", end="| ")
    print("14.doc file ", end="| ")
    print("15.ghi file phong ban ", end="| ")
    print("16.them 4 PB random", end="| ")
    print("17.them 12 NV random", end="| ")
    print("0.Thoat")
    print("=" * 70)

    lc = int(input("nhap lua chon: "))
    if lc == 1:
        try:
            NV.Create(phongban, danhsach)
        except ValueError as e:
            print(f"Loi~ : {e}")
    elif lc == 2:
        danhsach.showDS()
    elif lc == 3:
        danhsach.Suathongtin(phongban)  # sửa thông tin 1 nhân viên theo tên
    elif lc == 4:
        danhsach.Xoa()  # xóa 1 nhân viên theo tên
    elif lc == 5:
        danhsach.Timkiem()
    elif lc == 6:
        phongban.showphongban()  # hiển thị danh sách phòng baan
    elif lc == 7:
        phongban.xoaphongban(danhsach)  # xóa phòng ban
    elif lc == 8:
        phongban.showphongban()
        old = input("nhap ten phong ban cu: ")
        new = input("nhap ten phong ban moi: ")
        phongban.capnhat(old, new, danhsach)  # thay đổi tên của 1 phòng ban
    elif lc == 9:
        phongban.hienthi_theophongban(danhsach)
    elif lc == 10:
        phongban.themphongban()  # thêm 1 phòng ban
    elif lc == 11:
        phongban.thongke_theophongban(danhsach)  # thêm 1 phòng ban
    elif lc == 12:
        phongban.soluong_theophongban(danhsach)
    elif lc == 13:
        danhsach.ghifile()
    elif lc == 14:
        danhsach.docfile(phongban)
    elif lc == 15:
        phongban.ghifile_phongban()
    elif lc == 16:
        phongban.them4phongban()
    elif lc == 17:
        danhsach.them12NVvao4PB(phongban)
    elif lc == 0:
        # exit(1)
        break
