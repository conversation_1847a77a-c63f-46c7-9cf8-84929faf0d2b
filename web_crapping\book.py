from bs4 import BeautifulSoup
import requests
from page.book_page import BookPage
import csv
import asyncio
import aiohttp

template = "https://books.toscrape.com/catalogue/page-{}.html"
links = [template.format(i + 1) for i in range(50)]


def simple_way():
    # time = 1
    # for num_page, url in enumerate(links, start=1):
    #     content = requests.get(url).content
    #     page = BookPage(content)
    #     print("=" * 30, num_page, "=" * 30)
    #     for t in page.books:
    #         print(t)
    #     time -= 1
    #     if time == 0:
    #         break

    def write_book_to_csv():
        with open("book.csv", "w", newline="", encoding="utf-8") as f:
            f.write("Title,Price,Rating\n")
            writter = csv.writer(f)
            time = 1
            for num_page, url in enumerate(links, start=1):
                content = requests.get(url).content
                page = BookPage(content)
                for t in page.books:
                    book = (t.title, t.price, t.rating)
                    writter.writerow(book)
                time -= 1
                if time == 0:
                    break


def fast_way():
    async def fetch(session, url):
        async with session.get(url) as response:
            return await response.text()

    async def fetch_all(session, urls):
        tasks = []
        for url in urls:
            tasks.append(fetch(session, url))
        return await asyncio.gather(*tasks)

    async def scrape_books():
        async with aiohttp.ClientSession() as session:
            html_contents = await fetch_all(session, links)
            for content in html_contents:
                page = BookPage(content)
                for t in page.books:
                    print(
                        f"Title: {t.title}, Price: {t.price}, Rating: {t.rating}")

    async def write_book_to_csv():
        async with aiohttp.ClientSession() as session:
            html_contents = await fetch_all(session, links)
            with open("book.csv", "w", newline="", encoding="utf-8") as f:
                f.write("Title,Price,Rating\n")
                writer = csv.writer(f)
                for content in html_contents:
                    page = BookPage(content)
                    for t in page.books:
                        book = (t.title, t.price, t.rating)
                        writer.writerow(book)

    asyncio.run(scrape_books())
    asyncio.run(write_book_to_csv())


def test():
    pass
    # URL = "https://books.toscrape.com/"
    # repon = requests.get(URL)
    # TITLE = "li.col-xs-6.col-sm-4.col-md-3.col-lg-3 article.product_pod h3 a"
    # PRICE = "li.col-xs-6.col-sm-4.col-md-3.col-lg-3 article.product_pod div.product_price p.price_color"
    # RATING = "li.col-xs-6.col-sm-4.col-md-3.col-lg-3 article.product_pod p"
    # IMAGE = "li.col-xs-6.col-sm-4.col-md-3.col-lg-3 article.product_pod div.image_container a img"
    # STOCK = "li.col-xs-6.col-sm-4.col-md-3.col-lg-3 article.product_pod div.product_price p.instock.availability"
    # soup = BeautifulSoup(repon.content, "html.parser")
    # a_tag = soup.select_one(TITLE)
    # price_tag = soup.select_one(PRICE)
    # rating_tag = soup.select_one(RATING)
    # image_tag = soup.select_one(IMAGE)
    # stock_tag = soup.select_one(STOCK)
    # print(stock_tag.text.strip())
    # image_link = URL + image_tag.attrs["src"]
    # picture_book = requests.get(image_link).content

    # print(a_tag.attrs["title"])
    # print(price_tag.string)
    # print(rating_tag["class"][1])
    # print(image_link)

    # # with open("img.jpg", "wb") as image:
    # #     image.write(picture_book)
