import requests
from bs4 import BeautifulSoup
import pandas as pd


def v1():
    html_content = """
<html>
<head>
<title>Example</title>
</head>
<body>
<h1>Hello, world!</h1>
<p>This is an example paragraph.</p>
</body>
</html>
"""
    soup = BeautifulSoup(html_content, "html.parser")
    url = "https://thongthai.work/"
    reponse = requests.get(url)
    if reponse.status_code == 200:
        soup = BeautifulSoup(reponse.text, "html.parser")  # neu trang web la html
        # a = soup.find_all(class_="entry-title")
        # for i in a:
        #     link = i.get("href")
        #     print(i.text)
        # ==============================================
        # a_tag = soup.find_all(
        #     class_="blog-entry-title entry-title"
        # )  # kiem tag ten a // class entry title(lay title web
        # # findall("a") lay the a
        # for a in a_tag:
        #     print(a.text)  # get_address
        # ===============================
        # p = soup.find_all('p', class_='article')
        # for i in p:
        #     print(i.text)
        # ==========================================================
        Art = soup.find_all(
            class_="blog-entry-title entry-title"
        )  # Duyệt từng phần tử  để tìm các phần tử <a> là con của nó
        for i in Art:
            print("hehehe: ",i.text)
            # Tìm tất cả các phần tử <a> là con của phần tử <p>hiện tại
            a_tags = i.find_all("a")
            # In ra các phần tử <a> tìm thấy (nếu có)
            if a_tags:
                print(f"Found <a> :")
                for a_tag in a_tags:
                    print(a_tag)
            else:
                print("No <a> tags ")
    else:
        print("error")
# v1()

def v2():
    url = "https://www.binance.com/vi/price/bitcoin"
    reponse = requests.get(url)
    if reponse.status_code == 200:
        soup = BeautifulSoup(reponse.text, "html.parser")  # neu trang web la html
        price = soup.find(class_="css-dbxihu").text
        print(price)
    else:
        print("error")


#  find(), find_all(), select()
def vd3_trump():
    url = "https://www.nytimes.com/interactive/2017/06/23/opinion/trumps-lies.html"
    repon = requests.get(url)
    soup = BeautifulSoup(repon.text, "html.parser")

    results = soup.find_all("span", attrs={"class": "short-desc"})

    records = []
    for result in results:
        date = result.find("strong").text[0:-1] + ",2017"
        lie = result.contents[1][1:-2]
        explanation = result.find("a").text[1:-1]
        url = result.find("a")["href"]
        print(date, lie, explanation, url)
    #     records.append((date, lie, explanation, url))
    # df = pd.DataFrame(records, columns=['date', 'lie','explanation', 'url'])
    # df['date'] = pd.to_datetime(df['date'])
    # df.to_csv('trump_lies.csv', index=False, encoding='utf-8')

def vd4_shopping():
    url = "https://www.jumia.co.ke/all-products/?sort=rating&price=45-26508627#catalog-listing"
    repon = requests.get(url)
    soup = BeautifulSoup(repon.content, "html.parser")
    products = soup.find_all("div", class_="info")
    for product in products:
        Name = product.find("h3", class_="name").text
        Price = product.find("div", class_="prc").text
        Rating = product.find("div", class_="stars _s").text
        Rating_num = product.find("div", class_="rev").text

        info = [Name, Price, Rating, Rating_num]
        print(info)

# vd4_shopping()
def bt1():
    for i in range(1, 3):
        url = f"https://fit.huit.edu.vn/thong-bao?page={i}"
        repon = requests.get(url)
        soup = BeautifulSoup(repon.content, "html.parser")
        stt = soup.find_all("div", class_="news-cont")
        for i in stt:
            Name = i.find("h3").text
            link = i.find("a")["href"]
            date = i.find("span").text.strip()
            print(f"Name: {Name}\nLink:{link}\ntime:{date}")

# bt1()
def bt2():
    url = "https://fit.huit.edu.vn/gioi-thieu/ban-chu-nhiem-khoa"
    repon = requests.get(url)
    soup = BeautifulSoup(repon.content, "html.parser")
    stt = soup.find_all("p", class_="MsoNormal")
    # print(stt)
    for i in stt:
        text = i.text.strip()
        if text=="":
            continue
        # print(text)
        if "TS." in text:
            name = i.find("strong").text.strip()
            print(f"Name: {name}")
        if "Email" in text:
            email = i.find("a").text
            print(f"Email: {email}")

# bt2()
def bt3():  # fail: web dong
    url = "https://topdev.vn"
    repon = requests.get(url)
    if repon.status_code == 200:
        print("OK")
        soup = BeautifulSoup(repon.content, "html.parser")
        CV = soup.find_all("div", class_="swiper-slide")
        print(CV)
        # for i in CV:
        #     a1 = i.find_all(
        #         "div",
        #         class_="min-h-[117px] CardJobList block rounded border border-solid border-white bg-white p-4 transition-all hover:shadow-md",
        #     )
        #     for j in a1:
        #         Name = j.find(
        #             "h3", class_="line-clamp-1 text-sm font-bold md:text-lg"
        #         )  # Trích xuất tên công việc từ thẻ <h3>
        #         print(f"Job: {Name}")
    else:
        print("NOT OK")

def bt4():
    url = "https://www.python.org/blogs"
    repon = requests.get(url)
    if repon.status_code == 200:
        print("OK")
        soup = BeautifulSoup(repon.text, "html.parser")
        STT = soup.find("ul", class_="list-recent-posts menu")
        # print(STT)
        
        a1=STT.find_all("li")
        for i in a1:
            Name = i.find("h3").text
            Date=i.find("time").text
            link=i.find("a")["href"]
            print(f"Title: {Name}\nDate:{Date}\nlink:{link}")
    else:
        print("NOT OK")
# bt4()
import feedparser
def bt5():
    url = "https://thanhnien.vn/rss/giao-duc.rss"
    feed = feedparser.parse(url)

    if feed.status == 200:
        print("OK")
        for i, entry in enumerate(feed.entries, start=1):
            title = entry.title
            print(f"{i}. Title: {title}")
            if i == 10:
                break
    else:
        print("NOT OK")
bt5()