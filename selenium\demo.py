from selenium import webdriver
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select

browser = webdriver.Chrome()
browser.get("https://quotes.toscrape.com/search.aspx")

author = Select(browser.find_element(By.XPATH, "/html/body/div/form/div[1]/div/select"))
author.select_by_value("Marilyn Monroe")

tag = Select(browser.find_element(By.XPATH, "/html/body/div/form/div[2]/div/select"))
tag.select_by_value("life")


# /html/body/div[1]/form/input[1]
search_btn = browser.find_element(By.XPATH, "/html/body/div/form/input[1]")
search_btn.click()

span_info = browser.find_elements(By.XPATH, "/html/body/div/div[2]/div/span")
for span in span_info:
    print(span.text)


time.sleep(5)
browser.quit()
