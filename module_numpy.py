import numpy as np

array = np.array([1, 2, 3, 4, 5])
print("Original array:", array)
added_array = array + 5
print("Array after adding 5:", added_array)
array1 = np.zeros(4)
# array1 = array1.astype(int)
print(array1)  # [0. 0. 0. 0.]
array1 = np.ones(4)
print(array1)  # [0. 0. 0. 0.]
print("================" * 5)
array1 = np.arange(5)
print("Using np.arange(5):", array1)
# create an array with values from 1 to 8 with a step of 2
array2 = np.arange(1, 9, 2)
print("Using np.arange(1, 9, 2):", array2)
print(np.empty(5))  # giống như np.zeros
print(np.random.rand(5))
# print("================" * 5)
# print("================" * 5)
# print("================" * 5)
# print("================" * 5)
# print("================" * 5)
# print("================" * 5)
# print("================" * 5)
# print("================" * 5)
# print("================" * 5)
# print("================" * 5)
# print("================" * 5)
# print("================" * 5)
# print("================" * 5)
