import time
import random


def choose_word():
    words = ["python", "hangman", "programming", "development"]
    return random.choice(words)


def word_display(word, guesses):
    display_word = ""
    for char in word:
        if char in guesses:
            display_word += char + " "
        else:
            display_word += "_ "
    return display_word


def winning_condition(updated_word, turns):
    if "_" not in updated_word:
        return 1
    if turns == 0:
        return 0
    return -1  # Continue game


def reveal_consonants(word):
    consonants = [char for char in word if char not in "aeiou"]
    revealed = random.sample(
        consonants, min(len(consonants), 2)
    )  # Reveal up to 2 consonants
    return "".join(revealed)


if __name__ == "__main__":
    name = input("What is your name? ")
    print("Hello, " + name + ", time to play hangman!")
    time.sleep(1)
    print("Start guessing...\n")
    time.sleep(0.5)

    word = choose_word()
    revealed_letters = reveal_consonants(word)
    guesses = revealed_letters  # Start with revealed letters
    turns = (
        len(word) - len(revealed_letters) + 2
    )  # Adjust turns based on revealed letters

    print(f"The word contains: {len(word)} letters.")
    print(f"Starting with revealed letters: {', '.join(revealed_letters)}")
    print(word_display(word, guesses))

    while turns > 0:
        print("\nYou have", turns, "guesses remaining")
        print(word_display(word, guesses))
        guess = input("\nGuess a character: ").lower()

        if guess in guesses:
            print("\nYou have already tried this letter")
            continue
        else:
            guesses += guess

        if guess not in word:
            print("\nWrong, try again")
            turns -= 1
        else:
            print("\nGood guess!")

        updated_word = word_display(word, guesses)
        flag = winning_condition(updated_word, turns)

        if flag == 0:
            print("\nYou Lose")
            print("The word was", word)
            break
        elif flag == 1:
            print("\nYou won!")
            print("You guessed", word, "correctly")
            break
