import numpy as np

race_results = np.array(
    [
        ("At The Back", 1.2, 3),
        ("Fast Eddie", 1.3, 1),
        ("Almost There", 1.1, 2),
    ],
    dtype=[
        ("horse_name", "U12"),
        ("price", "f4"),
        ("position", "i4"),
    ],
)

# T<PERSON>y cập tên ngựa
# print(race_results["horse_name"])

# # Sắp xếp theo thứ tự vị trí
# sorted_results = np.sort(race_results, order="position")[["horse_name", "price"]]
# print(sorted_results)

# # Tìm tên ngựa chiến thắng
# winning_horse = race_results[race_results["position"] == 1]["horse_name"]
# print(winning_horse)
# ////////////////////////////////////////////////////////////////////////
import numpy.lib.recfunctions as rfn
from pathlib import Path

# Định ngh<PERSON>a kiểu dữ liệu cho các tệp
issued_dtypes = [
    ("id", "i8"),
    ("payee", "U10"),
    ("amount", "f8"),
    ("date_issued", "U10"),
]

cashed_dtypes = [
    ("id", "i8"),
    ("amount", "f8"),
    ("date_cashed", "U10"),
]

# Đọc dữ liệu từ tệp
issued_checks = np.loadtxt(
    Path("issued_checks.csv"),
    delimiter=",",
    dtype=issued_dtypes,
    skiprows=1,
)

cashed_checks = np.loadtxt(
    Path("cashed_checks.csv"),
    delimiter=",",
    dtype=cashed_dtypes,
    skiprows=1,
)

# Nối các mảng
cashed_check_details = rfn.rec_join(
    "id",
    issued_checks,
    cashed_checks,
    jointype="inner",
)

# # Hiển thị kết quả
# print(cashed_check_details[["payee", "date_issued", "date_cashed"]])
# print(cashed_check_details.dtype)
# outstanding_checks = [
#     check_id for check_id in issued_checks["id"] if check_id not in cashed_checks["id"]
# ]
# print(outstanding_checks)

# print(rfn.find_duplicates(np.ma.asarray(issued_checks)))
# ////////////////////////////////////////////////////////////////////////////////////////////////
import numpy as np
import numpy.lib.recfunctions as rfn

# Định nghĩa kiểu dữ liệu cho hành khách
passenger_dtype = [
    ("passenger_no", "i8"),  # Số hành khách
    ("first_name", "U20"),  # Tên
    ("last_name", "U20"),  # Họ
]

# Định nghĩa kiểu dữ liệu cho hộ chiếu
passport_dtype = [
    ("passport_no", "i8"),  # Số hộ chiếu
    ("passenger_no", "i8"),  # Số hành khách
    ("nationality", "U20"),  # Quốc tịch
]

# Đọc dữ liệu từ tệp passengers.csv
passengers = np.unique(
    np.loadtxt(
        "passengers.csv",
        delimiter=",",
        dtype=passenger_dtype,
        skiprows=1,
    ),
    axis=0,
)

# Đọc dữ liệu từ tệp passports.csv
passports = np.unique(
    np.loadtxt(
        "passports.csv",
        delimiter=",",
        dtype=passport_dtype,
        skiprows=1,
    ),
    axis=0,
)

# Nối các mảng hành khách và hộ chiếu
flight_details = rfn.rec_join(
    "passenger_no",
    passengers,
    passports,
    jointype="inner",
)

# Hiển thị tên, họ và quốc tịch của hành khách
print(flight_details[["first_name", "last_name", "nationality"]])
# //////////////////////////////////////////////////////////////////////
# Tìm hành khách không có hộ chiếu
passengers_without_passports = [
    passenger
    for passenger in passengers["passenger_no"]
    if passenger not in passports["passenger_no"]
]

print(passengers_without_passports)
# //////////////////////////////////////////////////////////////////////
# Tìm hộ chiếu không thuộc về hành khách nào
passports_without_passengers = [
    passenger
    for passenger in passports["passenger_no"]
    if passenger not in passengers["passenger_no"]
]

print(passports_without_passengers)
