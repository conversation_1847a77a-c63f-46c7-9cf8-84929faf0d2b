from abc import ABC, abstractmethod
from datetime import date

def class1():
    class SubsetsCalculator:
        def __init__(self):
            pass

        def f1(self, lst):
            lst.sort()  # Sắp xếp danh sách đầu vào
            self.f2(
                [], lst
            )  # G<PERSON><PERSON> phương thức f2 với danh sách trống và danh sách đã sắp xếp

        def f2(self, current_subset, remaining_elements):
            print(current_subset)  # In ra tập hợp con hiện tại
            for i in range(len(remaining_elements)):
                # Tạo một tập hợp con mới bằng cách thêm phần tử tiếp theo vào tập hợp con hiện tại
                new_subset = current_subset + [remaining_elements[i]]
                # G<PERSON>i đệ quy với tập hợp con mới và các phần tử còn lại sau phần tử vừa thêm
                self.f2(new_subset, remaining_elements[i + 1 :])

        # Hàm main để chạy chương trình


    calculator = SubsetsCalculator()
    # Nhập danh sách từ người dùng
    numbers = input("Nhập danh sách các số nguyên cách nhau bằng khoảng trắng: ").split()
    numbers = [int(num) for num in numbers]  # Chuyển đổi các chuỗi thành số nguyên
    calculator.f1(numbers)  # Gọi phương thức f1 với danh sách số nguyên


def class6():
    class WordPlay:
        def __init__(self):
            self.danhsach = []

        def get(self, words: list):
            self.danhsach = words

        def words_with_length(self, length):
            return [word for word in self.danhsach if len(word) == length]

        def started_with(self, s):
            return [word for word in self.danhsach if word.startswith(s)]

        def end_with(self, s):
            return [word for word in self.danhsach if word.endswith(s)]

        def only(self, L):
            return [
                word for word in self.danhsach if all(letter in word for letter in L)
            ]

            for word in self.word_list:
                # Kiểm tra xem tất cả các chữ cái trong từ có nằm trong danh sách L không
                if all(
                    letter in L for letter in word
                ):  # tất cả các chữ cái trong từ word đều có trong danh sách L
                    filtered_words.append(word)

        def avoids(self, L):
            return [
                word
                for word in self.danhsach
                if not any(letter in word for letter in L)
            ]

            filtered_words = []  # Danh sách lưu trữ các từ đã lọc
            for word in self.danhsach:
                # Kiểm tra xem từng chữ cái trong từ có nằm trong danh sách L không
                if not any(letter in L for letter in word):
                    filtered_words.append(
                        word
                    )  # Nếu không, thêm từ vào danh sách đã lọc
            return filtered_words

    word = WordPlay()
    # st = "hello xin chao cac ban den voi chanel cua minh, minh la cris devil gamer"
    # ds = st.split()
    # print(ds)
    word.get(["apple", "banana", "pear", "peach", "plum", "apricot", "pineapple"])
    print(word.danhsach)
    print(word.started_with("a"))
    print(word.end_with("e"))
    print(word.words_with_length(4))
    print(word.only(["a", "b"]))
    print(word.avoids(["b", "e"]))





def BTVN4():

    class GiaoDich:
        def __init__(self, Ma, ngay, dongia, soluong):
            self.Ma = Ma
            self.ngay = ngay
            self.dongia = dongia
            self.soluong = soluong
            self.thanhtien = self.soluong * self.dongia

    class GiaoDichVang(GiaoDich):
        def __init__(self, Ma, ngay, dongia, soluong, loai):
            super().__init__(Ma, ngay, dongia, soluong)
            self.loai = loai

    class GiaoDichTienTe(GiaoDich):
        def __init__(self, Ma, ngay, tygia, soluong, loaitiente, loai):
            super().__init__(Ma, ngay, tygia, soluong)
            self.loaitiente = loaitiente
            self.loai = loai
            if loai == "mua":
                self.thanhtien = self.soluong * self.dongia
            else:
                self.thanhtien = (self.soluong * self.dongia) * 1.05

    class QuanLyGiaoDich:
        def __init__(self):
            self.ds_giaodich = []

        def nhap_giaodich(self, giaodich):
            self.ds_giaodich.append(giaodich)

        def xuat_giaodich(self):
            for gd in self.ds_giaodich:
                if isinstance(gd, GiaoDichVang):
                    vang = {1: "18k", 2: "24k", 3: "9999"}
                    print(
                        f"Giao dich vang - Ma: {gd.Ma}, Ngay: {gd.ngay}, So luong: {gd.soluong}, Loai: {vang[gd.loai]}, Thanh tien: {gd.thanhtien}"
                    )
                elif isinstance(gd, GiaoDichTienTe):
                    tien = {1: "USD", 2: "EUR", 3: "AUD"}
                    buysell = {1: "mua", 2: "ban"}
                    print(
                        f"Giao dich tien te: {gd.Ma}, Ngay: {gd.ngay}, So luong: {gd.soluong}, Loai tien te: {tien[gd.loaitiente]}, Loai giao dich: {buysell[gd.loai]}, Thanh tien: {gd.thanhtien}"
                    )

        def tong_soluong_theoloai(self):
            tong_soluong_vang_18k = 0
            tong_soluong_vang_24k = 0
            tong_soluong_vang_9999 = 0
            tongmua = 0
            tongban = 0
            for gd in self.ds_giaodich:
                if isinstance(gd, GiaoDichVang):
                    if gd.loai == 1:
                        tong_soluong_vang_18k += gd.soluong
                    elif gd.loai == 2:
                        tong_soluong_vang_24k += gd.soluong
                    elif gd.loai == 3:
                        tong_soluong_vang_9999 += gd.soluong
                if isinstance(gd, GiaoDichTienTe):
                    if gd.loai == 1:
                        tongmua += gd.soluong
                    elif gd.loai == 2:
                        tongban += gd.soluong
            return (
                tong_soluong_vang_18k,
                tong_soluong_vang_24k,
                tong_soluong_vang_9999,
                tongmua,
                tongban,
            )

        def tong_thanhtien_theoloai(self):
            tong_thanhtien_vang_18k = 0
            tong_thanhtien_vang_24k = 0
            tong_thanhtien_vang_9999 = 0
            tongmua = 0
            tongban = 0
            for gd in self.ds_giaodich:
                if isinstance(gd, GiaoDichVang):
                    if gd.loai == 1:
                        tong_thanhtien_vang_18k += gd.thanhtien
                    elif gd.loai == 2:
                        tong_thanhtien_vang_24k += gd.thanhtien
                    elif gd.loai == 3:
                        tong_thanhtien_vang_9999 += gd.thanhtien
                if isinstance(gd, GiaoDichTienTe):
                    if gd.loai == 1:
                        tongmua += gd.thanhtien
                    elif gd.loai == 2:
                        tongban += gd.thanhtien
            return (
                tong_thanhtien_vang_18k,
                tong_thanhtien_vang_24k,
                tong_thanhtien_vang_9999,
                tongmua,
                tongban,
            )

    def menu():
        ds_giaodich = QuanLyGiaoDich()
        while True:
            print("1: Tao giao dich")
            print("2 : Xuat cac gia dich")
            print("3 : tong thanh tien theo loai")
            print("4 : tong so luong theo loai")
            lc = int(input("nhap lc :"))
            if lc == 1:
                ma = input("nhap ma GD: ")
                ngay = input("nhap ngay GD(dd/mm/yyyy): ")
                # ngay_date = datetime.datetime.strptime(ngay, "%d/%m/%Y") # chuyen doi thanh datetime theo dinh dang :
                # ngay_thang_str = ngay_thang_dt.strftime("%d/%m/%Y") # xuat datetime theo dinh dang
                soluong = int(input("nhap so luong: "))
                vang_te = int(
                    input("1: Tao Giao dich vang \t 2: Tao gia dich Tien te\t :")
                )
                if vang_te == 1:

                    dongia = int(input("nhap don gia: "))
                    loai = int(input("nhap loại: 1: 18k \t 2: 24k \t 3:9999\t :"))
                    vang = GiaoDichVang(ma, ngay, dongia, soluong, loai)
                    ds_giaodich.nhap_giaodich(vang)
                else:
                    tygia = int(input("nhap ty gia : "))
                    loaitiente = int(
                        input("nhap loại tien te: 1: USD \t 2: EUR \t 3: AUD\t :")
                    )
                    loai = int(input("nhap loại: 1: mua \t 2: ban\t :"))
                    tiente = GiaoDichTienTe(ma, ngay, tygia, soluong, loaitiente, loai)
                    ds_giaodich.nhap_giaodich(tiente)
            if lc == 2:
                print("Danh sach giao dich:")
                ds_giaodich.xuat_giaodich()
            if lc == 3:
                tong18k, tong24k, tong9999, tongmua, tongban = (
                    ds_giaodich.tong_thanhtien_theoloai()
                )
                print("\nTong thanh tien theo loai giao dich:")
                print(
                    f"18k: {tong18k}, 24k:{tong24k}, 9999:{tong9999}, mua:{tongmua}, ban:{tongban}"
                )
            if lc == 4:
                sl18, sl24, sl999, slmua, slban = ds_giaodich.tong_soluong_theoloai()
                print("\nTong so luong theo loai giao dich :")
                print(
                    f"18k: {sl18}, 24k:{sl24}, 9999:{sl999}, mua:{slmua}, ban:{slban}"
                )

            tt = int(input("tiep tuc ?? 1: yes \t 0: no\t :"))
            if tt == 0:
                break

    menu()



