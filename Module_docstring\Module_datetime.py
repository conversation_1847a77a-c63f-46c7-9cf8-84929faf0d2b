from datetime import *


def date_class():
    print(dir(date))
    print(date.today().ctime())
    print(date.fromisocalendar(2023, 34, 1))  # tạo đối tượng nằm 23, tuần 34 và thứ 2
    print(date.today().isocalendar())  # tr<PERSON> về năm, tuần và thứ
    print(date.today().isoformat())
    print(date.fromisoformat("1999-08-21"))
    print(date.today().replace(year=2060))
    print(date.resolution)  #  độ chính xác nhỏ nhất có thể đại diện (1 ngày).
    print(date.today().strftime("%d/%m/%Y"))
    print(date.today().toordinal())  # 1/1/1 to now


def time_class():
    print(dir(time))
    print(time(14, 45).replace(hour=16))
    print(time.fromisoformat("12:30:00"))
    print(time(12, 30).isoformat())
    print(time(15, 30).strftime("%H:%M:%S"))


def datetime_class():
    print(dir(datetime))
    print(datetime.now().astimezone(pytz.timezone("US/Eastern")))  # Chuyển đổi giờ khác
    print(datetime.combine(date.today(), time(12, 30)))  #  date + time => datetime
    print(datetime.now().ctime())
    print(datetime.now().time())
    print(
        datetime.fromisocalendar(2023, 35, 2)
    )  # tạo đối tượng nằm 23, tuần 35 và thứ 3
    print(datetime.fromisoformat("2023-08-22 14:30:00"))
    print(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print(datetime.strptime("25 December, 2022", "%d %B, %Y"))  # chuỗi thành datetime


def timedelta_class():
    print(dir(timedelta))
    print(repr(timedelta(days=1, hours=5)))
    td = timedelta(days=5, hours=10, minutes=30)
    print("Days:", td.days)  # Số ngày
    print("Seconds:", td.seconds)  # Số giây
    print("Microseconds:", td.microseconds)  # Số microseconds
    print("Tổng số giây:", td.total_seconds())  # Tổng số giây

    # Sử dụng các dunder methods
    print("Absolute value:", abs(timedelta(days=-5)))
    print("Cộng timedelta:", timedelta(days=5) + timedelta(days=3))
    print("Trừ timedelta:", timedelta(days=5) - timedelta(days=3))
    print("Nhân timedelta với số:", timedelta(days=2) * 2)
    print("Chia timedelta:", timedelta(days=10) / 2)
    print("Chia lấy phần nguyên:", timedelta(days=10) // 3)
    print("Lấy phần dư:", timedelta(days=10) % timedelta(days=3))
    print("Phủ định timedelta:", -timedelta(days=5))

    current_time = datetime.now()
    new_time = current_time + timedelta(days=1, hours=3)
    print("Thời gian hiện tại:", current_time)
    print("Thời gian mới sau khi cộng timedelta:", new_time)


def module_time():

    import time

    print(dir(time))
    print(time.ctime(time.time()))  # thời gian hiện tại
    print(time.asctime())  # default: time.localtime()
    print(
        time.get_clock_info("perf_counter")
    )  # Trả về thông tin về đồng hồ có tên name
    print(time.localtime())
    print(time.process_time())  # thời gian CPU sử dụng
    time.sleep(0.2)
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    print(time.strptime("2024-08-21", "%Y-%m-%d"))
    print(time.time())  # 1/1/1 -> now : (s)
    print(time.thread_time())
    print(time.tzname)  # múi giờ địa phương
    ####
    start = time.perf_counter()
    time.sleep(0.5)
    end = time.perf_counter()
    print(f"Elapsed time: {end - start} seconds")


def module_pytz():
    import pytz

    print(dir(pytz))
    tz = pytz.timezone("America/New_York")
    dt = datetime.now(tz)
    print(dt.strftime("%Y-%m-%d %H:%M:%S %Z%z"))
    print(datetime.now(pytz.utc).strftime("%Y-%m-%d %H:%M:%S %Z%z"))
    # print("All timezones:", pytz.all_timezones[:]) # Asia/Ho_Chi_Minh
    # print("Common timezones:", pytz.common_timezones[:])
    print("Country name for US:", pytz.country_names["VN"])
