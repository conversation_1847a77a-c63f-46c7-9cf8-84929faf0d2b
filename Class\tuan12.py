import tkinter as tk
from tkinter import messagebox
from tkinter import PhotoImage
from PIL import Image, ImageTk
def handle_custom_event(event):
    messagebox.showinfo("Custom Event", "Hello " + entry.get())
def check_entry_and_fire_event():
    if entry.get() == "khanh":
        button.event_generate("<<CustomEvent>>")
    else:
        messagebox.showinfo("Button click event", "Nornal Event")
def button_click(event):
    print("Button is clicked!")
def key_press(event):
    print("Key", event.keysym, "is pressed!")
def mouse_motion(event):
    print("Mouse is moving at x =", event.x, ", y =", event.y)
class CustomButton(tk.Button):
    def __init__(self, master=None, **kwargs):
        super().__init__(master, **kwargs)
        self.config(bg="blue", fg="white", font=("Arial",12))
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)
    def on_enter(self, event):
        self.config(bg="darkblue")
    def on_leave(self, event):
        self.config(bg="blue")
class ValidatedEntry(tk.Entry):
    def __init__(self, master=None, **kwargs):
        super().__init__(master, **kwargs)
        self.bind("<KeyRelease>", self.validate)
    def validate(self, event):
        current_value = self.get()
        if current_value.isdigit():
            self.config(bg="blue")
        elif current_value=="":
            self.config(bg="white")
        else:
            self.config(bg="red")
class CustomProgressBar(tk.Frame):
    def __init__(self, master=None, width=200, height=20,**kwargs):
        super().__init__(master, **kwargs)
        self.width = width
        self.height = height
        self.canvas = tk.Canvas(self, width=self.width,
        height=self.height, bg="white", highlightthickness=0)
        self.canvas.pack()
        self.progress = self.canvas.create_rectangle(0, 0,0, self.height, fill="blue")
        self.current_progress = 0
    def set_progress(self, value):
        width = self.width * (value / 100)
        self.canvas.coords(self.progress, 0, 0, width,self.height)
    def start_progress(self, duration):
        self.current_progress = 0
        self.step = 100 / (duration * 1000 / 100) # mỗi
        self.update_progress()
    def update_progress(self):
        if self.current_progress < 100:
            self.current_progress += self.step
            self.set_progress(self.current_progress)
            self.after(100, self.update_progress)
        else:
            self.set_progress(100) 
            messagebox.showinfo("Hoàn thành", "Tiến trình đã hoàn thành!") # Hiển thị hộp thoại thông báo
class CustomLabel(tk.Label):
    def __init__(self, master=None, text="Custom Label",
                 font=("Arial", 12), fg_color="black", 
                 bg_color="white",borderwidth=2, relief="solid",
                 **kwargs):
        super().__init__(master, text=text, font=font,
                         fg=fg_color, bg=bg_color, 
                         borderwidth=borderwidth,relief=relief,
                         **kwargs)
        self.font = font
        self.fg_color = fg_color
        self.bg_color = bg_color
        self.borderwidth = borderwidth
        self.relief = relief
class CustomWidget(tk.Frame):
    def __init__(self, master=None, title="Custom Widget",
                 image_path="", **kwargs):
        super().__init__(master, **kwargs)
        self.config(bg="gray", bd=2, relief="sunken")
        self.title_label = tk.Label(self, text=title,
                                    font=("Arial", 16), 
                                    bg="red")
        self.title_label.pack(pady=10)
        self.image = PhotoImage(file=image_path)
        self.image_label = tk.Label(self,image=self.image,
                                    bg="blue")
        self.image_label.pack(pady=5)
        self.label = tk.Label(self, text="Enter something:",
                              font=("Arial", 12), bg="brown")
        self.label.pack(pady=5)
        self.entry = tk.Entry(self, font=("Arial", 12))
        self.entry.pack(pady=5)
        self.button = tk.Button(self, text="Submit",
                                font=("Arial", 12), 
                                command=self.on_submit)
        self.button.pack(pady=10)
        self.result_label = tk.Label(self, text="",
                                     font=("Arial", 12),
                                     bg="lightgray")
        self.result_label.pack(pady=5)
    def on_submit(self):
        entry_text = self.entry.get()
        self.result_label.config(text=f"You entered: {entry_text}")
class CustomWidget(tk.Frame):
    def __init__(self, master=None, title="Custom Widget",
                 initial_image_path="", **kwargs):
        super().__init__(master, **kwargs)
        self.changed = True
        self.config(bg="lightgray", bd=2, relief="sunken")  
        self.title_label = tk.Label(self, text=title,
                                    font=("Arial", 16), bg="lightgray")
        self.title_label.pack(pady=10)
        # Thêm hình ảnh đầu tiên
        self.image1 = self.resize_image(initial_image_path)
        self.image1_label = tk.Label(self,
                                     image=self.image1, bg="lightgray")
        self.image1_label.pack(pady=5)
        
        
        self.change_button = tk.Button(self, text="Change Image", 
                                       font=("Arial", 12), command=self.change_image)

        self.change_button.pack(pady=10)
    def resize_image(self, image_path):
        image = Image.open(image_path)
        # image = image.resize((300, 300),Image.Resampling.LANCZOS)        
        return ImageTk.PhotoImage(image)
    def change_image(self):
        # Thay đổi hình ảnh của widget
        if self.changed :
            new_image_path = "asd.png"
        else:
            new_image_path = "b.png"
        self.changed = not self.changed
        new_image = self.resize_image(new_image_path)
        self.image1_label.config(image=new_image)
        self.image1 = new_image

root = tk.Tk()
root.title("Custom Widget Example")
# custom_button = CustomButton(root, text="Click Me")
# custom_button.pack(pady=20)
# =============================================================================
# 
# =============================================================================
# validated_entry = ValidatedEntry(root)
# validated_entry.focus()
# validated_entry.pack(pady=20)
# =============================================================================
# 
# =============================================================================
# progress_bar = CustomProgressBar(root)
# progress_bar.pack(pady=20)
# # Nút để bắt đầu tiến trình
# start_button = tk.Button(root, text="Start Progress",
# command=lambda: progress_bar.start_progress(10))
# start_button.pack(pady=10)
# =============================================================================
# 
# =============================================================================
# label1 = CustomLabel(root, text="Hello, World!",
# font=("Helvetica", 16), fg_color="blue",
# bg_color="lightyellow", borderwidth=3, relief="groove")
# label1.pack(pady=10)
# label2 = CustomLabel(root, text="Tkinter Custom Label",
# font=("Times New Roman", 18, "italic"), fg_color="white",
# bg_color="green", borderwidth=5, relief="ridge")
# label2.pack(pady=10)
# label3 = CustomLabel(root, text="Another Custom Label",
# font=("Courier", 14, "bold"), fg_color="red",
# bg_color="black", borderwidth=2, relief="solid")
# label3.pack(pady=10)
# =============================================================================
# 
# =============================================================================
# image_path = "a.png"
# # Tạo và hiển thị custom widget
# custom_widget = CustomWidget(root, title="My Custom Widget", 
#                              image_path=image_path)
# custom_widget.pack(padx=20, pady=20)
# =============================================================================
# 
# =============================================================================
# initial_image_path = "a.png"
# custom_widget = CustomWidget(root, title="My Custom Widget", 
#                              initial_image_path=initial_image_path)
# custom_widget.pack(padx=20, pady=20)
# =============================================================================
# 
# =============================================================================

# button = tk.Button(root, text="Click Me")
# button.pack(padx=20, pady=20)
# button.bind("<Button-1>", button_click)
# button.unbind("<Button-1>")
# root.bind("<KeyPress>", key_press)
# root.bind("<Motion>", mouse_motion)
# =============================================================================
# 
# =============================================================================
entry = tk.Entry(root)
entry.focus()
entry.pack(padx=20, pady=10)
button = tk.Button(root, text="Click Me", command=check_entry_and_fire_event)
button.pack(pady=10)
# Gắn sự kiện tùy chỉnh với Button
button.bind("<<CustomEvent>>", handle_custom_event)













root.mainloop()