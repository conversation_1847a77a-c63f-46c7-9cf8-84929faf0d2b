import sqlite3

# text real integer blob:image


def book():
    con = sqlite3.connect("books.db")
    cur = con.cursor()  # thuc thi cac cau lenh

    # sql_com = "create table book (id integer primary key autoincrement, name text not null, author text not null)"
    # cur.execute(sql_com)
    # sql_com = """insert into book  (name,author) values
    # ('sach A',' <PERSON>'),
    # ('sach B',' Jack'),
    # ('sach C',' Zhi'),
    # ('sach D',' Xun')"""
    # cur.execute(sql_com)
    sql_com = """insert into book  (name,author) values (?,?)"""
    lst = [
        ("python", " <PERSON>"),
        ("C program", " CiaoMeng"),
        ("CleanCode", " Bravo"),
        ("Duck", " Batman"),
        ("Spider", " Batman"),
    ]
    # cur.executemany(sql_com, lst)
    # a = """update book set author ='khanh' where id=1"""
    # cur.execute(a)
    cur.execute("SELECT * FROM book")
    for row in cur.fetchall():
        print(row)
    con.commit()

    con.close()


con = sqlite3.connect(":memory:")
# con = sqlite3.connect("books.db")
cur = con.cursor()

# Tạo bảng ví dụ
cur.execute(
    """
    CREATE TABLE if not exists books (
        title TEXT NOT NULL,
        author TEXT NOT NULL,
        year INTEGER not null
    )
"""
)

# Thêm dữ liệu mẫu
cur.execute("INSERT INTO books VALUES ('Brave New World', 'Aldous Huxley', 1932);")
_ = [("1984", "George Orwell", 1949), ("One piece", "Luffy", 1999)]
cur.executemany("INSERT INTO books VALUES (?,?,?);", _)
# ================================================================================================
sql_upd = """update books 
set author ='Huslang'
where title='Brave New World';"""
cur.execute(sql_upd)
# ================================================================================================
sql_del = """
delete from books
where year = 1999;
"""
cur.execute(sql_del)
# ================================================================================================
cur.execute("SELECT * FROM books")
print(cur.fetchall())  # In ra dữ liệu
# for row in cur:
#     print(row)
# ================================================================================================
con.commit()
con.close()
