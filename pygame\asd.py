def wait_screen_window():
    import pygame
    from collections import namedtuple
    from random import randint

    Colour = namedtuple("Colour", ["red", "green", "blue"])

    BACKGROUND_COLOUR = Colour(red=36, green=188, blue=168)
    BALL_COLOUR = Colour(red=255, green=253, blue=65)

    BALL_RADIUS = 40

    pygame.init()
    pygame.display.set_caption("Bouncing Ball")

    clock = pygame.time.Clock()
    screen = pygame.display.set_mode()

    def main():
        ball_position = [(screen.get_width() // 2), (screen.get_height() // 2)]
        ball_velocity = [0, 0]
        while ball_velocity[0] == 0 or ball_velocity[1] == 0:
            ball_velocity = [randint(-5, 5), randint(-5, 5)]

        while True:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return

            screen.fill(BACKGROUND_COLOUR)
            pygame.draw.circle(screen, BALL_COLOUR, ball_position, BALL_RADIUS)
            pygame.display.update()

            if ball_position[0] - BALL_RADIUS < 0:
                ball_velocity[0] = -ball_velocity[0]
            elif ball_position[0] + BALL_RADIUS > screen.get_width():
                ball_velocity[0] = -ball_velocity[0]

            # Check for top and bottom collisions
            if ball_position[1] - BALL_RADIUS < 0:
                ball_velocity[1] = -ball_velocity[1]
            elif ball_position[1] + BALL_RADIUS > screen.get_height():
                ball_velocity[1] = -ball_velocity[1]
            ball_position[0] += ball_velocity[0]
            ball_position[1] += ball_velocity[1]
            clock.tick(500)

    main()


wait_screen_window()


def test():
    import pygame
    from collections import namedtuple

    Colour = namedtuple("Colour", ["red", "green", "blue"])
    BACKGROUND_COLOUR = Colour(red=36, green=188, blue=168)
    RECTANGLE_COLOUR = Colour(red=255, green=200, blue=205)
    CIRCLE_COLOUR = Colour(red=255, green=253, blue=65)
    pygame.init()
    pygame.display.set_caption("My game!")

    screen = pygame.display.set_mode([640, 480])
    screen.fill(BACKGROUND_COLOUR)

    pygame.draw.rect(screen, RECTANGLE_COLOUR, [50, 80, 100, 50])
    pygame.draw.circle(screen, CIRCLE_COLOUR, [320, 240], 40)

    font = pygame.font.Font(None, 28)
    text = font.render("hehe", True, (0, 0, 0))
    screen.blit(text, (screen.get_width() // 2, (screen.get_height() // 2)))

    pygame.display.update()

    def main():
        while True:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return

    main()


# test()
def mouse_track():
    import pygame

    pygame.init()

    pygame.display.set_caption("Mousetracker")
    screen = pygame.display.set_mode([640, 480])

    def main():
        while True:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return
                elif event.type == pygame.MOUSEMOTION:
                    position = event.__dict__["pos"]
                    print(position)

    main()


# mouse_track()


def mouse2():

    import pygame
    from collections import namedtuple

    Colour = namedtuple("Colour", ["red", "green", "blue"])

    BACKGROUND_COLOUR = Colour(red=36, green=188, blue=168)
    CIRCLE_COLOUR = Colour(red=255, green=253, blue=65)

    pygame.init()

    pygame.display.set_caption("Mousetracker")

    clock = pygame.time.Clock()
    screen = pygame.display.set_mode([640, 480])

    def main():
        circle_position = (screen.get_width() // 2), (screen.get_height() // 2)

        while True:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return
                elif event.type == pygame.MOUSEMOTION:
                    circle_position = event.__dict__["pos"]

            screen.fill(BACKGROUND_COLOUR)
            pygame.draw.circle(screen, CIRCLE_COLOUR, circle_position, 20)
            pygame.display.update()

            clock.tick(60)

    main()


# mouse2()
def dichuyen():
    import pygame
    from collections import namedtuple

    Colour = namedtuple("Colour", ["red", "green", "blue"])

    BACKGROUND_COLOUR = Colour(red=36, green=188, blue=168)
    CIRCLE_COLOUR = Colour(red=255, green=253, blue=65)

    pygame.init()

    pygame.display.set_caption("My game!")

    clock = pygame.time.Clock()
    screen = pygame.display.set_mode([640, 480])

    def main():
        circle_position = [320, 240]

        while True:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return

            screen.fill(BACKGROUND_COLOUR)
            pygame.draw.circle(screen, CIRCLE_COLOUR, circle_position, 40)
            pygame.display.update()

            circle_position[0] += 5

            clock.tick(15)

    main()


# dichuyen()
