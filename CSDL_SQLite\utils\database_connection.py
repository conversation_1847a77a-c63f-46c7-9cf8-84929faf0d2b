import sqlite3


class DatabaseConnection:
    def __init__(self, host):
        self.host = host
        self.connection = None

    def __enter__(self):
        """Create a new database connection"""
        self.connection = sqlite3.connect(self.host)
        return self.connection

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Close the database connection
        exc_type: loại exception
        exc_val: gi<PERSON> trị của exception
        exc_tb: traceback c<PERSON>a chứa thông tin về ngăn xếp cuộc gọi
        """
        self.connection.commit()
        self.connection.close()
