import tkinter as tk
from tkinter import *
from tkinter import PhotoImage
from PIL import Image, ImageTk
import datetime
from datetime import date
import tkinter.messagebox as mb
from tkcalendar import Calendar
import re
import json


def class1():
    def say_hello():
        today = datetime.datetime.today()
        format_date = today.strftime("%d/%m/%Y - %H/%M")
        time_out.config(text=format_date, bg="aquamarine")

    def show_text():
        ten = name_in.get()
        name_out.config(text=f"xin chao {ten} dep trai ")

    def show_selection():
        selection = ""
        if LPL.get():
            selection += "Trung Quoc được chọn. "
        if LCK.get():
            selection += "Han <PERSON>uoc đượ<PERSON> chọn. "
        option.config(text=selection)

    def show_selection2():
        lc = radio_var.get()
        if lc == 1:
            out.config(text="Yeu")
        elif lc == 2:
            out.config(text="Gioi")
        else:
            out.config(text="Xuat sac")

    def show_selected3():
        selected_items = list.curselection()
        selection = ""
        for index in selected_items:
            selection += list.get(index) + "\n"
        outlist.config(text=selection)

    def get_text():
        text_content = text.get("1.0", tk.END)
        print("Nội dung văn bản: ", text_content)
        out_text.config(text=text_content)

    def mess():
        mb.showinfo("Thông báo", "Đây là một thông báo thông tin!")

    def test1():
        root = tk.Tk()
        root.title("Hello Tkinter")
        label = tk.Label(root, text="Chào mừng bạn đến với Tkinter!")
        label.pack()
        button = tk.Button(
            root,
            text="click to quit",
            command=root.destroy,
            background="red",
            fg="white",
        )
        button.pack()
        root.mainloop()

    # test1()

    """
    lable : root,text font bg fg width/height justify
    button: root,text ,command,font ,bg, fg, width/height ,state:normal/disable, relief
    entry:root, width,show,bg,fg,,font,state,justify,insertbackground
    checkbutton:root,text,variable(chon || ko chon), onvalue/offvalue(T || F),command,bg,fg,font
    radiobutton:root,text,variable(int || string),command,bg,fg,font
    listbox:root,height,selectmode(single,browse,extend,multiple),bg,fg,font,width,activestyle(dotox,underline,defauult,none),
    text:root,height,width,bg,fg,font,wrap,insertbackground,insertwidth,state,yscrollcommand/xscrollcommand:
    """

    root = tk.Tk()
    root.title("Test1")  # dat title
    root.resizable(1, 1)  # false false: cann't change
    root.geometry("900x600+200+50")  # size ban dau`num3:truc X num4:trucY
    root.configure(bg="papayawhip", borderwidth=10, relief="ridge")
    label = tk.Label(text="Chào mừng đến với tkinter!", fg="darkred")
    label.pack(pady=5)

    # #==========================================================================

    # label = tk.Label(root, text="Them Nhan", fg="rosybrown", bg="paleturquoise").pack(
    #     pady=10
    # )
    # label1 = tk.Label(
    #     root,
    #     text="Label 1",
    #     font=("Arial", 12, "underline"),
    #     fg="mediumseagreen",
    #     bg="orchid",
    # ).pack()
    # label2 = tk.Label(
    #     root,
    #     text="Lable 2 can giua~",
    #     font=("Times New Roman", 10, "italic"),
    #     height=2,
    #     justify="center",
    #     bg="lightblue",
    # ).pack()
    # label3 = tk.Label(
    #     root,
    #     text="Label 3 rong va cao",
    #     width=300,
    #     bg="lightgreen",
    #     fg="purple",
    #     font=("Times New Roman", 12, "bold"),
    # ).pack()
    # =================================================================

    # # Tạo một đối tượng PhotoImage từ đường dẫn của hình ảnh
    # photo = PhotoImage(file="asd.png")
    # # Tạo nhãn và hiển thị hình ảnh
    # label = tk.Label(root, image=photo, width=350, height=300)
    # label.pack()

    # with Image.open("asd.png") as img:
    #     img.thumbnail((150, 100))  # Thay đổi kích thước
    #     width, height = img.size
    #     print(f"W:{width}\t H:{height}")
    # photo = ImageTk.PhotoImage(img)
    # png = tk.Label(root, image=photo)
    # png.pack(pady=10)

    # # =================================================================
    # image = Image.open("ad.jpg")
    # image = image.resize((150, 160), Image.BILINEAR)  # auto-fixed
    # # Chuyển đổi hình ảnh thành định dạng có thể sử dụng trong tkinter
    # photo = ImageTk.PhotoImage(image)
    # # Tạo nhãn và hiển thị hình ảnh
    # jpg = tk.Label(root, image=photo)
    # jpg.pack(pady=5)

    # a, b = image.size
    # print(f"{a}\t{b}")
    # =================================================================
    # Tạo nút để thực hiện hành động
    # time_out = tk.Label(root, text="click to show datetime")
    # time_out.pack(pady=10)
    # button = tk.Button(
    #     root,
    #     text="Click me!",
    #     command=say_hello,
    #     width=15,
    #     height=1,
    #     bg="orange",
    #     fg="navy",
    #     font=("Arial", 10, "bold"),
    #     relief="solid",
    #     bd=5,
    # ).pack()
    # # =================================================================
    # name_in = tk.Entry(
    #     root, width=40, font=("Times New Roman", 12), justify="left", state="normal"
    # )
    # name_in.pack(pady=10)
    # # Tạo nút để hiển thị văn bản trong ô nhập liệu

    # button = tk.Button(root, text="show ten", command=show_text).pack()
    # # Nhãn để hiển thị văn bản từ ô nhập liệu
    # name_out = tk.Label(root, text="")
    # name_out.pack(pady=10)
    # # =================================================================
    # # checkbutton
    # LPL = tk.BooleanVar()
    # LCK = tk.BooleanVar()
    # checkbutton1 = tk.Checkbutton(root, text="T1", variable=LCK).pack()
    # checkbutton3 = tk.Checkbutton(root, text="GENG", variable=LCK).pack()
    # checkbutton2 = tk.Checkbutton(root, text="WBG", variable=LPL).pack()
    # checkbutton4 = tk.Checkbutton(root, text="JDG", variable=LPL).pack()
    # button = tk.Button(root, text="Hiển thị tùy chọn", command=show_selection).pack()
    # # Nhãn để hiển thị tùy chọn đã chọn
    # option = tk.Label(root, text="")
    # option.pack()
    # =================================================================
    # radiobutton (1 in 2)
    radio_var = tk.IntVar()  # hoac StringVar
    radiobutton1 = tk.Radiobutton(root, text="0d", variable=radio_var, value=1).pack()
    radiobutton4 = tk.Radiobutton(root, text="2d", variable=radio_var, value=1).pack()
    radiobutton2 = tk.Radiobutton(root, text="8d", variable=radio_var, value=2).pack()
    radiobutton3 = tk.Radiobutton(root, text="10d", variable=radio_var, value=3).pack()
    button = tk.Button(root, text="Hiển thị", command=show_selection2).pack()
    out = tk.Label(root, text="")
    out.pack()
    # =================================================================

    list = tk.Listbox(root, height=5, selectmode=tk.EXTENDED, activestyle="dotbox",width=60)
    list.pack(pady=10)
    # Thêm mục vào Listbox
    for i in ["Mục 1", "Mục 2", "Mục 3"]:
        list.insert(tk.END, i)
    list.insert(tk.END, "Mục 4")
    # Tạo nút để hiển thị các mục đã chọn
    button = tk.Button(root, text="Hiển thị", command=show_selected3).pack()
    # Nhãn để hiển thị các mục đã chọn
    outlist = tk.Label(root, text="")
    outlist.pack()
    # =================================================================
    text = tk.Text(
        root,
        height=3,
        width=50,
        wrap=tk.CHAR,
        insertbackground="cornflowerblue",
        insertwidth=10,
        state="normal",
    )# warp theo tu` hoac ki tu hoac 1 dong`

    text.pack(pady=10, padx=50)
    button = tk.Button(root, text="Lấy văn bản", command=get_text).pack(pady=5)
    out_text = tk.Label(root, text="")
    out_text.pack()

    # =================================================================
    mb.showinfo("Thông báo", "Đây là một thông báo thông tin!")
    mb.showwarning("Cảnh báo", "Đây là một thông báo cảnh báo!")
    mb.showerror("Lỗi", "Đã xảy ra một lỗi!")

    response = mb.askquestion("Xác nhận", "Bạn có chắc chắn muốn tiếp tục?")
    if response == "yes":
        print("Đã chọn Yes")
    else:
        print("Đã chọn No")

    response = mb.askyesno("Xác nhận", "Bạn có chắc chắn muốn tiếp tục?")
    if response:
        print("Đã chọn Yes")
    else:
        print("Đã chọn No")

    response = mb.askokcancel("Xác nhận", "Bạn có chắc chắn muốn tiếp tục?")
    if response:
        print("Đã chọn OK")
    else:
        print("Đã chọn Cancel")

    response = mb.askretrycancel("Xác nhận", "Đã xảy ra một lỗi. Bạn có muốn thử lại?")
    if response:
        print("Đã chọn Retry")
    else:
        print("Đã chọn Cancel")

    button = tk.Button(text="nhan vao day", command=mess)
    button.pack()
    button = tk.Button(
        root, text="click to quit", command=root.destroy, background="red"
    ).pack(pady=5)
    root.mainloop()

class1()
def bt3():

    def submit():
        content = input.get()
        ouput.config(text=content)

    root = tk.Tk()
    root.title("BT3")
    root.geometry("600x400+200+100")
    input = tk.Entry(root, width=40, justify="left")
    input.pack()
    button1 = tk.Button(root, text="submit", command=submit)
    button1.pack()
    ouput = tk.Label(root, text=" ", bg="red")
    ouput.pack()

    root.mainloop()


def Dang_ky():

    data_list=[]
    def get_selected_date():
        date = cal.selection_get()
        tuoi = date.today().year - date.year
        ten=hoten.get().strip()
        passs=pas.get().strip()
        passs2=pas2.get().strip()
        mail=email.get().strip()
        if passs=="" or passs2=="" or ten=="" or mail=="":
            mb.showerror("Lỗi", "Thieu thong tin")
            return
        if not is_valid_email(mail):
            mb.showerror("Lỗi", "Email khong dung")
            return
        if passs!=passs2:
            mb.showerror("Lỗi", "Mat khau sai")
            return
        if tuoi<18:
            mb.showerror("Lỗi", "Chua du 18t")
            return
        print(f"thong tin:{date.strftime("%d/%m/%Y")}\t{ten}\t{mail}\t{passs2}\t{tuoi}")
        ghifile(ten, tuoi, date, mail, passs)

    def kiem_tra_email_trung(mail, data_list):
        for data in data_list:
            if data["email"] == mail:
                return True
        return False
    def ghifile(ten,tuoi,date,mail,passs):

        data = {
            "name": ten,
            "age": tuoi,
            "date": date.strftime("%d/%m/%Y"),
            "email": mail,
            "pass": passs
        }
        if kiem_tra_email_trung(mail, data_list):
            mb.showerror("Lỗi đăng ký", "Email đã tồn tại")
            return False
        else:
            data_list.append(data)
            with open("user.json", "w") as file:
                json.dump(data_list, file, indent=4)
            return True

    def is_valid_email(email):
        pattern = r"^[a-zA-Z0-9._%+-]+@[a-z]+\.[a-zA-Z]{2,4}$"  # ^: bat dau la ki tu hoac chuoi ki tu
        # $: ket thuc phai la ki tu hoac chuoi ki tu
        if re.match(pattern, email):
            return True
        else:
            return False


    root = tk.Tk()
    root.title("BT4")  # dat title
    root.resizable(1, 1)  # false false: cann't change
    root.geometry("900x600+200+50")  # size ban dau`num3:truc X num4:trucY
    root.configure(bg="pink", borderwidth=10)
    # Tiêu đề
    tb = tk.Label(root, text="Đăng ký", fg="darkred",font=("Times New Roman",14,"bold"))
    tb.pack(pady=5)

    # Nhập tên
    tk.Label(root, text="Nhập Tên: ").pack(pady=5)
    hoten = tk.Entry(root, width=40)
    hoten.pack(pady=5)

    # Chọn ngày tháng năm sinh
    tk.Label(root, text="Chọn Ngày Sinh: ").pack(pady=5)
    cal = Calendar(root, selectmode="day")
    cal.pack(pady=5)

    # Nhập email
    tk.Label(root, text="Nhập Địa Chỉ Email: ").pack(pady=5)
    email = tk.Entry(root, width=40)
    email.pack(pady=5)

    # Nhập mật khẩu
    tk.Label(root, text="Nhập Mật Khẩu: ").pack(pady=5)
    pas = tk.Entry(root, width=40, show="*")
    pas.pack(pady=5)

    # Nhập lại mật khẩu
    tk.Label(root, text="Nhập Lại Mật Khẩu: ").pack(pady=5)
    pas2 = tk.Entry(root, width=40, show="*")
    pas2.pack(pady=5)

    # Nút đăng ký
    submit_button = tk.Button(root, text="Đăng Ký", command=get_selected_date)
    submit_button.pack(pady=5)
    button = tk.Button(
            root, text="click to quit", command=root.destroy, background="red"
        ).pack(pady=5)
    root.mainloop()
def Dang_nhap():

    def check_acc():
        mail=email.get().strip()
        password=pas.get().strip()
        flag=False
        with open("user.json","r") as file:
            data=json.load(file)
        for i in data:
            if i["email"] == mail:
                if i["pass"] == password:
                    mb.showinfo("Thong bao","dang nhap thanh cong")
                    flag=True
                else:
                    mb.showinfo("Thong bao","Sai mat khau")
                    flag=True
        if not flag:
            mb.showinfo("Thong bao","sai email")

    root = tk.Tk()
    root.title("BT4")  # dat title
    root.resizable(1, 1)  # false false: cann't change
    root.geometry("900x600+200+50")  # size ban dau`num3:truc X num4:trucY
    root.configure(bg="antiquewhite", borderwidth=5)
    # Tiêu đề
    tb = tk.Label(root, text="Đăng nhập", fg="darkred",font=("Arial",14,"bold"))
    tb.pack(pady=5)
    tk.Label(root, text="Nhập Địa Chỉ Email: ").pack(pady=5)
    email = tk.Entry(root, width=40)
    email.pack(pady=5)

    # Nhập mật khẩu
    tk.Label(root, text="Nhập Mật Khẩu: ").pack(pady=5)
    pas = tk.Entry(root, width=40, show="*")
    pas.pack(pady=5)
    submit_button = tk.Button(root, text="Đăng Nhập", command=check_acc)
    submit_button.pack(pady=5)
    switch_dangky = tk.Button(
                root, text="Đăng ký", command=Dang_ky).pack(pady=5)
    button = tk.Button(
                root, text="click to quit", command=root.destroy, background="red"
            ).pack(pady=5)
    root.mainloop()
