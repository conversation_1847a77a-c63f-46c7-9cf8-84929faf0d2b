from pathlib import Path


def print_separator():
    """In ra một dòng phân cách."""
    print("=" * 50)


def initialize_paths():
    """Khởi tạo và in ra đường dẫn cơ bản."""
    BASE_DIR = Path("D:/MyProject").resolve()  # Thay đổi đường dẫn
    print(BASE_DIR)
    print_separator()
    print(Path.cwd())


# initialize_paths()


def list_directory_contents():
    """Liệt kê các thư mục và file trong thư mục hiện tại."""
    print_separator()
    for p in Path("D:/Kah-book").iterdir():  # Thay đổi đường dẫn
        print(p)


# list_directory_contents()


def create_and_manipulate_files():
    """Tạo, đổi tên và lấy thông tin về file và thư mục."""
    my_dir = Path("D:/MyProject/panda")  # Thay đổi đường dẫn
    myfile = Path("D:/MyProject/example.txt")  # Thay đổi đường dẫn
    newfile = my_dir / "new_file.txt"  # Tạo file mới (g<PERSON><PERSON> lập)

    print(my_dir.name, my_dir.stem, my_dir.suffix)
    print(myfile.name, myfile.stem, myfile.suffix)
    print(newfile.exists(), newfile.name, newfile.stem, newfile.suffix)
    print_separator()
    print(my_dir.parent.absolute(), my_dir.absolute().parent, my_dir.absolute())
    print(myfile.parent.resolve())
    print(newfile.parent.parent, newfile.parent)


# create_and_manipulate_files()


def resolve_paths():
    """Sử dụng các phương thức để giải quyết đường dẫn."""
    print_separator()
    for path in [
        Path("..").absolute(),
        Path("..").resolve(),
        Path("D:/MyProject").resolve(),
    ]:  # Thay đổi đường dẫn
        print(path)


# resolve_paths()


def search_files():
    """Tìm kiếm các file trong thư mục."""
    p = Path.home() / ".nuget"  # Giả lập thư mục trong user
    print_separator()
    for i in p.glob("*vscode*"):
        print(i)
    for i in p.rglob("*.json"):
        print(i)


# search_files()


def create_and_remove_directories():
    """Tạo và xóa thư mục."""
    print_separator()
    p = Path("D:/new_folder")  # Thay đổi đường dẫn
    p.mkdir(exist_ok=True)  # Tạo thư mục, không gây lỗi nếu đã tồn tại
    p = Path("D:/new_folder/tempdir/subdir")  # Thay đổi đường dẫn
    p.mkdir(parents=True, exist_ok=True)  # Tạo thư mục con

    # Xóa thư mục
    p.rmdir()  # Xóa thư mục (chỉ khi thư mục rỗng)


# create_and_remove_directories()


def file_operations():
    """Thao tác với file: tạo, đổi tên và xóa file."""
    p = Path("D:/new_folder/haha.docx")  # Thay đổi đường dẫn
    try:
        p.touch()  # Tạo file (không ghi đè)
        p.rename("D:/new_folder/haha_rename.docx")  # Đổi tên file
    except:
        p.replace("D:/new_folder/huhu.txt")  # Thay thế file
        p.unlink()  # Xóa file

    print_separator()


# file_operations()


def new_functionality():
    """Chứa các ví dụ mới về thao tác với file và đường dẫn."""
    path = Path("D:/home/<USER>/proj/readme.md")
    target = path.with_suffix(".txt")  # .md -> .txt
    print(target)

    filename = "example.txt"
    with open(filename) as file:
        contents = file.read()
    print(contents)

    path = Path("example.txt")
    contents = path.read_text()
    print(contents)

    home = Path.home()
    config_path = home / ".my_config.toml"
    print(f"haha {config_path}")

    full_path = Path("D:/home/<USER>/example.txt")
    print(full_path.name)
    print(full_path.parent)
    print(full_path.suffix)
    print(full_path.stem)

    project = Path("D:/Code/python/Module_docstring")
    project = Path.cwd()  # Lấy thư mục hiện tại
    for path in project.iterdir():
        if path.is_file():
            print(
                f"{path} >>>>>>>  {path.relative_to(project)}"
            )  # Đổi thành đường dẫn tương đối

    for path in Path.cwd().glob("*.py"):
        print(path)


# new_functionality()
from pathlib import Path
from pprint import pprint


def file_operations():
    """
    Nhóm các thao tác đọc, ghi, quản lý file và thuộc tính của file.
    Bao gồm: đọc nội dung, ghi nội dung, đổi tên, xóa file, lấy thông tin file.
    """
    # Đọc nội dung tệp
    content = Path(r"D:\Code\python\Foo.txt")
    print("Nội dung tệp Foo.txt:")
    print(content.read_text())

    # Ghi nội dung vào tệp
    new_file_path = Path("D:/example.txt")
    new_file_path.write_text("from pathlib python ")
    print(f"Nội dung đã được ghi vào {new_file_path}.")

    # Đổi tên tệp
    try:
        renamed_path = new_file_path.rename("D:/renamed.txt")
        print(f"Tệp đã được đổi tên thành: {renamed_path}")
    except FileExistsError:
        print("Tệp đích đã tồn tại. Không thể đổi tên.")

    # Xóa tệp
    new_file_path.unlink(missing_ok=True)
    print("Tệp example.txt đã được xóa (nếu có).")

    # Lấy thông tin về tệp
    print(f"Tên tệp: {new_file_path.name}")
    print(f"Thư mục cha: {new_file_path.parent}")
    print(f"Phần mở rộng: {new_file_path.suffix}")
    print(f"Tên tệp không có phần mở rộng: {new_file_path.stem}")


def path_operations():
    """
    Nhóm các thao tác liên quan đến đường dẫn.
    Bao gồm: kiểm tra loại đường dẫn, tạo thư mục, lấy đường dẫn tuyệt đối, tương đối.
    """
    # Kiểm tra đường dẫn có phải là tệp không
    is_file = Path("D:/example.txt").is_file()
    print(f"Có phải là tệp không: {is_file}")

    # Kiểm tra đường dẫn có phải là thư mục không
    is_dir = Path("D:/example_dir").is_dir()
    print(f"Có phải là thư mục không: {is_dir}")

    # Tạo thư mục mới
    Path("D:/new_folder").mkdir(mode=0o755, parents=True, exist_ok=True)
    print("Thư mục mới đã được tạo.")

    # Lấy đường dẫn tuyệt đối
    abs_path = Path("D:/example.txt").resolve()
    print(f"Đường dẫn tuyệt đối: {abs_path}")

    # Lấy đường dẫn tương đối
    relative_path = Path("../../stuff/nut.txt")
    print("Nội dung tệp nut.txt:")
    print(relative_path.read_text())

    # Đường dẫn tương đối với tổ tiên
    relative_path = Path("D:/desktop/krmt/exam.txt").relative_to("D:/")
    print(f"Đường dẫn tương đối: {relative_path}")


def directory_and_traversal_operations():
    """
    Nhóm các thao tác liên quan đến thư mục và duyệt đường dẫn.
    Bao gồm: lấy danh sách tổ tiên, liệt kê files/thư mục, tìm file theo mẫu, duyệt cây thư mục.
    """
    # Lấy danh sách tổ tiên
    parents_list = list(Path("D:/example.txt").parents)
    print("Danh sách các thư mục cha:")
    print(parents_list)

    # Liệt kê các tệp/thư mục
    files_and_dirs = list(Path("D:/").iterdir())
    print("Danh sách tệp/thư mục trong D:/:")
    pprint(files_and_dirs)

    # Tìm tệp theo mẫu
    matching_files = list(Path("D:/").glob("*.txt"))
    print("Tệp .txt trong D:/:")
    pprint(matching_files)

    # Tìm tệp đệ quy theo mẫu
    recursive_files = list(Path("D:/Code/python").rglob("*.txt"))
    print("Tệp .txt đệ quy trong D:/Code/python:")
    pprint(recursive_files)

    # Duyệt qua cây thư mục
    for path in Path(r"D:\tai lieu\TAI LIEU_MMT_2023\MMT").rglob("*"):
        print(f">>> {path}")


def some_methods():
    # # Khởi tạo đường dẫn
    base = Path("D:/")
    path = base / "readme.txt"
    new_dir = base / "sub"

    # 1. Đọc toàn bộ nội dung tệp
    content = path.read_text()
    print("Nội dung tệp:", content)  # 'Line 1\nLine 2\n'

    # 2. Ghi nội dung vào tệp
    path.write_text("from readme.txt 2.0")
    print(f"Nội dung đã ghi vào tệp {path.name}")

    # 3. Lấy đường dẫn tuyệt đối ~ 8
    absolute_path = path.resolve()
    print("Đường dẫn tuyệt đối:", absolute_path)  # Path('/home/<USER>/proj/readme.md')

    # 4. Lấy tên tệp
    file_name = path.name
    print("Tên tệp:", file_name)  # 'readme.md'

    # 5. Lấy thư mục cha
    parent_dir = path.parent
    print("Thư mục cha:", parent_dir)  # Path('home/trey/proj')

    # 6. Lấy phần mở rộng tệp
    file_suffix = path.suffix
    print("Phần mở rộng tệp:", file_suffix)  # '.md'

    # 7. Lấy tên tệp không có phần mở rộng
    file_stem = path.stem
    print("Tên tệp không có phần mở rộng:", file_stem)  # 'readme'

    # 8. Đường dẫn tương đối với tổ tiên ~ 3
    relative_path = Path("D:\Code\C\LT\sort.cpp").relative_to(base)
    print("Đường dẫn tương đối với tổ tiên:", relative_path)  # Path('readme.md')

    # 9. Kiểm tra đường dẫn có phải là tệp không
    is_file = path.is_file()
    print("Có phải là tệp không:", is_file)  # True

    # 10. Kiểm tra đường dẫn có phải là thư mục không
    is_dir = path.is_dir()
    print("Có phải là thư mục không:", is_dir)  # False

    # 11. Tạo thư mục mới
    new_dir.mkdir(parents=True, exist_ok=True)
    print(f"Thư mục mới đã được tạo: {new_dir}")

    # 12. Lấy thư mục hiện tại
    current_dir = Path.cwd()
    print("Thư mục hiện tại:", current_dir)  # Path('/home/<USER>/proj')

    # 13. Lấy thư mục gốc của người dùng
    home_dir = Path.home()
    print("Thư mục gốc của người dùng:", home_dir)  # Path('/home/<USER>')

    # 14. Lấy danh sách tổ tiên
    parents_list = list(path.parents)
    print("Danh sách tổ tiên:", parents_list)  # [Path('/home/<USER>/proj'), ...]

    # 15. Liệt kê các tệp/thư mục
    for item in new_dir.iterdir():
        print("Tệp/thư mục:", item)

    # 16. Tìm tệp theo mẫu
    pattern = "*.c"
    files_found = list(base.glob(pattern))
    print("Tìm tệp theo mẫu:", files_found)  # [Path('/home/<USER>/proj/readme.md')]

    # 17. Tìm tệp đệ quy theo mẫu
    files_found_recursive = list(base.rglob(pattern))
    print(
        "Tìm tệp đệ quy theo mẫu:", files_found_recursive
    )  # [Path('/home/<USER>/proj/readme.md')]

    # 18. Kết hợp các phần của đường dẫn
    new_file_path = base / "sub" / "f.txt"
    print("Đường dẫn kết hợp:", new_file_path)  # Path('/home/<USER>/proj/sub/f.txt')

    # 19. Lấy kích thước tệp (bytes)
    file_size = path.stat().st_size
    print("Kích thước tệp (bytes):", file_size)  # Kích thước cụ thể

    # 20. Duyệt qua cây thư mục
    for dirpath, dirnames, filenames in new_dir.walk():
        print(f"Thư mục: {dirpath}, Thư mục con: {dirnames}, Tệp: {filenames}")

    # 21. Đổi tên đường dẫn
    new_name_path = path.rename(new_file_path)
    print("Đường dẫn mới sau khi đổi tên:", new_name_path)

    # 22. Xóa tệp
    path.unlink()
    print(f"Tệp đã xóa: {path.name}")
