import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.feature_selection import SequentialFeatureSelector
from sklearn.preprocessing import StandardScaler
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.ensemble import GradientBoostingClassifier
import numpy as np


df = pd.read_csv("All_Countries.csv")
# print(df.info())
# print(df.sample(5))
# =====================================================================================
df_filled = df.fillna(0)
# print(df_filled.info())
# print(df.isnull().sum().sort_values(ascending=False).to_string())
df_null_count = df.isnull().sum().sort_values(ascending=False)
# print(df_null_count[df_null_count == 0].index)
# print(df.describe().columns)
# print((set(df.describe().columns) & set(df_null_count[df_null_count == 0].index)))


# ===================================country gdp =NaN====================================================
# missing_gdp_countries = df[df["gdp"].isna()]["country"]
# print(missing_gdp_countries)

# ==================column has max(3) Nan value(datetime,obj,float,...), drop row has Nan value in that column===================
columns_with_few_nans = df.isnull().sum()[df.isnull().sum() <= 3].index
# print(columns_with_few_nans)
# print(df[df[columns_with_few_nans].isnull().any(axis=1)])
df_cleaned = df.dropna(subset=columns_with_few_nans)
# print(df_cleaned)

# ================================================================================================================
df_null_count = df.isnull().sum().sort_values(ascending=False)

good_cols = list(
    set(df_null_count[df_null_count <= 3].index) & set(df.describe().columns)
)

df_cleaned = df.dropna(axis="index", subset=good_cols).copy()
# print(df_cleaned)

# =================================================================================================
# model = LinearRegression(
#     fit_intercept=True
# )  # tính toán và sử dụng hệ số chặn. Hệ số chặn là giá trị của Y khi X = 0.
# X = df_cleaned[["population"]]
# Y = df_cleaned["gdp"]
# model.fit(X, Y)
# print(model.coef_)  #  sự thay đổi của GDP khi dân số tăng lên một đơn vị
# print(model.intercept_)  # hệ số chặn: giá trị của GDP khi dân số bằng 0
# print(model.score(X, Y))  # độ phù hợp của mô hình hồi quy với dữ liệu


# # ================================================================
# model = LinearRegression(fit_intercept=True)
# X = df_cleaned[["population", "rural_population", "median_age", "life_expectancy"]]
# Y = df_cleaned["gdp"]
# model.fit(X, Y)
# print(model.coef_)
# print(model.intercept_)
# print(model.score(X, Y))

# ================================================================================================
# numerical_cols = df_cleaned.select_dtypes(include=np.number).columns.tolist()
# model = LinearRegression(fit_intercept=True)
# X = df_cleaned[numerical_cols]  # Sử dụng tất cả các cột số
# Y = df_cleaned["gdp"]  # Cột mục tiêu là GDP
# X = df_cleaned[numerical_cols].dropna()  # Loại bỏ các hàng có NaN
# Y = df_cleaned["gdp"][X.index]  # Đảm bảo Y có cùng index với X sau khi dropna
# model.fit(X, Y)

# # In ra các hệ số và R-squared
# print(model.coef_)
# print(model.intercept_)
# print(model.score(X, Y))

# ===================================================================
# print(good_cols)
# X = df_cleaned[[x for x in good_cols if x != "life_expectancy"]]
# Y = df_cleaned["life_expectancy"]
# model = LinearRegression(fit_intercept=True)
# model.fit(X, Y)
# print(model.coef_)
# print(model.intercept_)
# print(model.score(X, Y))
# for col, coef in zip(X.columns, model.coef_):
#     print("%s: %.3e" % (col, coef))
# =================================================================

# # Initializing the Linear Regression model
# model = LinearRegression(fit_intercept=True)

# # Perform Sequential Feature Selector
# sfs = SequentialFeatureSelector(model, n_features_to_select=5)
# X = df_cleaned[[x for x in good_cols if x != "life_expectancy"]]
# Y = df_cleaned["life_expectancy"]
# sfs.fit(X, Y)  # Uses a default of cv=5
# selected_feature = list(X.columns[sfs.get_support()])
# # print("Feature selected for highest predictability:", selected_feature)


# model = LinearRegression(fit_intercept=True)
# X = df_cleaned[selected_feature]
# Y = df_cleaned["life_expectancy"]
# model.fit(X, Y)
# print(model.score(X, Y))
# for col, coef in zip(X.columns, model.coef_):
#     print("%s: %.3e" % (col, coef))
# print("Intercept:", model.intercept_)
# ================================================================
per_capita = [
    "gdp",
    "land_area",
    "forest_area",
    "rural_land",
    "agricultural_land",
    "urban_land",
    "population_male",
    "population_female",
    "urban_population",
    "rural_population",
]
for col in per_capita:
    df_cleaned[col] = df_cleaned[col] / df_cleaned["population"]

col_to_use = per_capita + [
    "nitrous_oxide_emissions",
    "methane_emissions",
    "fertility_rate",
    "hospital_beds",
    "internet_pct",
    "democracy_score",
    "co2_emissions",
    "women_parliament_seats_pct",
    "press",
    "electricity_access_pct",
    "renewable_energy_consumption_pct",
]

# model = LinearRegression(fit_intercept=True)
# sfs = SequentialFeatureSelector(model, n_features_to_select=6)
# X = df_cleaned[col_to_use]
# Y = df_cleaned["life_expectancy"]
# sfs.fit(X, Y)  # Uses a default of cv=5
# selected_feature = list(X.columns[sfs.get_support()])
# print("Feature selected for highest predictability:", selected_feature)
# ================================================================================================
# # Bước 1: Chuẩn hóa dữ liệu
# scaler = StandardScaler()
# X_scaled = scaler.fit_transform(df_cleaned[col_to_use])  # Chuẩn hóa dữ liệu
# Y = df_cleaned["life_expectancy"]

# # Bước 2: Chạy Sequential Feature Selector với dữ liệu đã chuẩn hóa
# model = LinearRegression(fit_intercept=True)
# sfs = SequentialFeatureSelector(model, n_features_to_select=6)
# sfs.fit(X_scaled, Y)
# selected_feature = list(pd.Series(col_to_use)[sfs.get_support()])
# print("Tính năng được chọn để có khả năng dự đoán cao nhất:", selected_feature)

# # Bước 3: Huấn luyện mô hình cuối cùng
# X_selected = df_cleaned[selected_feature]
# X_selected_scaled = scaler.fit_transform(X_selected)

# final_model = LinearRegression(fit_intercept=True)
# final_model.fit(X_selected_scaled, Y)

# # Bước 4: In kết quả
# print("\nR^2 Score ", final_model.score(X_selected_scaled, Y))
# print("Intercept:", final_model.intercept_)

# # Hiển thị hệ số của các feature đã chọn
# for col, coef in zip(selected_feature, final_model.coef_):
#     print(f"{col}: {coef:.3e}")
# =========================================================================================
df_cleaned["north"] = df_cleaned["latitude"] > 0

# model = DecisionTreeClassifier(max_depth=3)
# X = df_cleaned[col_to_use]
# Y = df_cleaned["north"]
# model.fit(X, Y)
# print(model.score(X, Y))
# print(Y.value_counts()) # 147 true , 40 false
# ================================================================================================

# model = RandomForestClassifier(n_estimators=5, max_depth=3)
# X = df_cleaned[col_to_use]
# Y = df_cleaned["north"]
# model.fit(X, Y)
# print(model.score(X, Y))

model = GradientBoostingClassifier(n_estimators=5, max_depth=3)
X = df_cleaned[col_to_use]
Y = df_cleaned["north"]
model.fit(X, Y)
model.score(X, Y)
# print(model.predict_proba(X))
# print(model.predict(X))

print(np.mean(model.predict_proba(X)[range(len(X)), model.predict(X).astype(int)]))
