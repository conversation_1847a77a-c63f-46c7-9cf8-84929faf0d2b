import os
import requests
from tkinter import *
import tkinter as tk
import tkinter.messagebox as mb
import re
import json

class Khachhang:
    def __init__(self, name, address, phonenumber, email):
        self.Name = name
        self.Address = address
        self.Phonenumber = phonenumber
        self.Email = email


class User:
    def __init__(self, name, address, phonenumber, email, username, password, role):
        self.Name = name
        self.Address = address
        self.Phonenumber = phonenumber
        self.Email = email
        self.Username = username
        self.Password = password
        self.Role = role


def is_valid_email(email):
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-z]+\.[a-zA-Z]{2,4}$"  # ^: bat dau la ki tu hoac chuoi ki tu
    # $: ket thuc phai la ki tu hoac chuoi ki tu
    if re.match(pattern, email):
        return True
    else:
        return False


def is_valid_phonenumber(phonenumber):
    if phonenumber.startswith("0") and len(phonenumber) >= 9 and phonenumber.isdigit():
        return True
    else:
        return False


def duplicate_phone_email_for_user(phone, email, role):
    try:
        with open("account.json", "r", encoding="utf-8") as f:
            customers = json.load(f)
            if customers == []:
                raise FileNotFoundError
        for customer in customers:
            if (
                customer["Phonenumber"] == phone or customer["Email"] == email
            ) and customer["Role"] == role:
                return False
        return True
    except FileNotFoundError:
        return True


def duplicate_phone_email_for_KH(phone, email):
    try:
        with open("khachhang.json", "r", encoding="utf-8") as f:
            customers = json.load(f)
            if customers == []:
                raise FileNotFoundError
        for customer in customers:
            if customer["Phonenumber"] == phone or customer["Email"] == email:
                return False
        return True
    except FileNotFoundError:
        return True


def reset_config():
    for widget in root.winfo_children():
        widget.destroy()
    for i in range(100):  # Xóa cấu hình cột
        root.columnconfigure(i, weight=0)
        root.rowconfigure(i, weight=0)


def AdminPanel():
    def crawl():
        def Crawl_DL():
            sl = int(sl_entry.get())
            users_info = []
            with open("khachhang.json", "r", encoding="utf-8") as file:
                customers = json.load(file)
            with open("khachhang.json", "w", encoding="utf-8") as file:
                for _ in range(sl):
                    url = "https://randomuser.me/api/"
                    response = requests.get(url)
                    if response.status_code == 200:
                        data = response.json()
                        user = data["results"][0]

                        # Trích xuất thông tin người dùng
                        name = f"{user['name']['first']} {user['name']['last']}"
                        address = f"{user['location']['street']['number']} {user['location']['street']['name']}, {user['location']['city']}, {user['location']['country']}"
                        phone = user["phone"]
                        email = user["email"]

                        users_info = Khachhang(name, address, phone, email)
                        customers.append(users_info.__dict__)
                        mb.showinfo("Thông báo", f"Đã thêm khách hàng: {name}")
                    else:
                        mb.showinfo(
                            "Thông báo", f"Failed to retrieve the data for user {_+1}"
                        )
                json.dump(customers, file, indent=4, ensure_ascii=False)

            refresh_listbox()
            add_window.destroy()

        add_window = tk.Toplevel(root)
        add_window.title("Crawl dữ liệu từ website")
        tk.Label(
            add_window, text="Nhập số lượng khách hàng bạn cần Crawl:", font="Times 20"
        ).grid(row=0, column=0)
        sl_entry = tk.Entry(add_window, width=30, font="Times 20")
        sl_entry.focus()
        sl_entry.grid(row=0, column=1)
        crawl = tk.Button(
            add_window, text="Tạo", font="Times 20", fg="red", command=Crawl_DL
        )
        crawl.grid(row=1, columnspan=2)

    def refresh_listbox():
        list.delete(0, tk.END)  # Clear the listbox
        try:
            with open("khachhang.json", "r", encoding="utf-8") as file:
                customers = json.load(file)
            for customer in customers:
                # In ra tên khách hàng để kiểm tra
                print(customer["Name"])
                list.insert(tk.END, customer["Name"])
        except FileNotFoundError:
            mb.showerror("Lỗi", "Không tìm thấy file khachhang.json.")
        except json.JSONDecodeError:
            mb.showerror("Lỗi", "Nội dung tệp khachhang.json không hợp lệ.")
        except KeyError as e:
            mb.showerror("Lỗi", f"Không tìm thấy khóa trong dữ liệu JSON: {e}")
        except Exception as e:
            mb.showerror("Lỗi", f"Đã xảy ra lỗi: {e}")

    def delete():
        selected_index = list.curselection()
        if not selected_index:
            mb.showwarning(
                "Chọn Khách Hàng", "Hãy chọn một khách hàng để Xóa khỏi danh sách."
            )
            return
        yesno = mb.askyesno("Delete", "Bạn có chắc chắn muốn xóa không ?")
        if yesno:
            index = selected_index[0]
            try:
                with open("khachhang.json", "r", encoding="utf-8") as file:
                    customers = json.load(file)
                    if customers == []:
                        raise FileNotFoundError
                customer_to_delete = customers.pop(index)
                with open("khachhang.json", "w") as file:
                    json.dump(customers, file, indent=4)
                list.delete(index)
                mb.showinfo(
                    "Xóa khách hàng",
                    f"Đã xóa thành công khách hàng {customer_to_delete['Name']}",
                )
            except IndexError:
                mb.showerror("Lỗi", "Không thể xóa khách hàng này.")
            except FileNotFoundError:
                mb.showerror("Lỗi", "Danh sách khách hàng trống")
            refresh_listbox()
        else:
            pass

    def detail():
        selected_index = list.curselection()
        if not selected_index:
            mb.showwarning(
                "Chọn Khách Hàng", "Hãy chọn một khách hàng để xem chi tiết khách hàng."
            )
            return
        index = selected_index[0]
        with open("khachhang.json", "r", encoding="utf-8") as file:
            customers = json.load(file)
        selected_customer = customers[index]
        mb.showinfo(
            "Thông tin chi tiết",
            f"Họ Tên: {selected_customer['Name']}\nEmail: {selected_customer['Email']}\nĐịa chỉ: {selected_customer['Address']}\nSố điện thoại: {selected_customer['Phonenumber']}",
        )

    def Creat():
        def create_customer():
            name = name_entry.get()
            email = email_entry.get()
            address = address_entry.get()
            phonenumber = phonenumber_entry.get()
            try:
                with open("khachhang.json", "r", encoding="utf-8") as file:
                    customers = json.load(file)
            except FileNotFoundError:
                customers = []

            if not (name and email and address and phonenumber):
                mb.showwarning("Lỗi", "Tất cả các trường phải được điền đầy đủ.")
                add_window.destroy()
                return
            if not is_valid_email(email) or not is_valid_phonenumber(phonenumber):
                mb.showwarning("Lỗi", "Email hoặc số điện thoại không đúng định dạng")
                add_window.destroy()
                return
            if not duplicate_phone_email_for_KH(phonenumber, email):
                mb.showwarning("Lỗi", "Email hoặc số điện thoại đã tồn tại")
                add_window.destroy()
                return
            new_KH = Khachhang(name, address, phonenumber, email)
            customers.append(new_KH.__dict__)
            with open("khachhang.json", "w", encoding="utf-8") as file:
                json.dump(customers, file, indent=4, ensure_ascii=False)
                mb.showinfo("Thành Công", "Khách hàng mới đã được thêm.")
            refresh_listbox()
            add_window.destroy()

        add_window = tk.Toplevel(root)
        add_window.title("Thêm Khách Hàng")
        tk.Label(add_window, text="Tên:", font="Times 20").grid(row=0, column=0)
        tk.Label(add_window, text="Email:", font="Times 20").grid(row=1, column=0)
        tk.Label(add_window, text="Địa chỉ:", font="Times 20").grid(row=2, column=0)
        tk.Label(add_window, text="Số điện thoại:", font="Times 20").grid(
            row=3, column=0
        )

        name_entry = tk.Entry(add_window, width=50, font="Times 20")
        name_entry.focus()
        email_entry = tk.Entry(add_window, width=50, font="Times 20")
        address_entry = tk.Entry(add_window, width=50, font="Times 20")
        phonenumber_entry = tk.Entry(add_window, width=50, font="Times 20")

        name_entry.grid(row=0, column=1)
        email_entry.grid(row=1, column=1)
        address_entry.grid(row=2, column=1)
        phonenumber_entry.grid(row=3, column=1)
        tk.Button(
            add_window, text="Tạo", command=create_customer, font="'Gadugi 20"
        ).grid(row=4, columnspan=2)
        tk.Button(
            add_window, text="Thoát", command=add_window.destroy, font="'Gadugi 20"
        ).grid(row=5, columnspan=2)

    def edit():
        selected_item = list.curselection()
        if not selected_item:
            mb.showwarning("Chọn Khách Hàng", "Hãy chọn một khách hàng để sửa.")
            return
        index = selected_item[0]
        with open("khachhang.json", "r", encoding="utf-8") as file:
            customers = json.load(file)
        customer = customers[index]
        edit_window = tk.Toplevel(root)
        edit_window.title("Sửa Khách Hàng")

        tk.Label(edit_window, text="Tên:", font="Times 20").grid(row=0, column=0)
        tk.Label(edit_window, text="Email:", font="Times 20").grid(row=1, column=0)
        tk.Label(edit_window, text="Địa chỉ:", font="Times 20").grid(row=2, column=0)
        tk.Label(edit_window, text="Số điện thoại:", font="Times 20").grid(
            row=3, column=0
        )

        name_entry = tk.Entry(edit_window, font="Times 20", width=50)
        name_entry.insert(0, customer["Name"])
        name_entry.focus()
        email_entry = tk.Entry(edit_window, font="Times 20", width=50)
        email_entry.insert(0, customer["Email"])
        address_entry = tk.Entry(edit_window, font="Times 20", width=50)
        address_entry.insert(0, customer["Address"])
        phonenumber_entry = tk.Entry(edit_window, font="Times 20", width=50)
        phonenumber_entry.insert(0, customer["Phonenumber"])

        name_entry.grid(row=0, column=1)
        email_entry.grid(row=1, column=1)
        address_entry.grid(row=2, column=1)
        phonenumber_entry.grid(row=3, column=1)

        def is_duplicate(email, phone):
            for i, cust in enumerate(customers):
                if i != index and (
                    cust["Email"] == email or cust["Phonenumber"] == phone
                ):
                    return True
            return False

        def save_changes():
            # Update customer information with new data
            new_name = name_entry.get()
            new_email = email_entry.get()
            new_address = address_entry.get()
            new_phonenumber = phonenumber_entry.get()
            if not is_valid_email(
                new_email
            ) or not is_valid_phonenumber(new_phonenumber):
                mb.showwarning("Lỗi", "Email hoặc số điện thoại đã tồn tại")
                edit_window.destroy()
                return
            if is_duplicate(new_email, new_phonenumber):
                mb.showwarning("Lỗi", "Email hoặc số điện thoại đã tồn tại")
                edit_window.destroy()
                return
            customers[index]["Name"] = new_name
            customers[index]["Email"] = new_email
            customers[index]["Address"] = new_address
            customers[index]["Phonenumber"] = new_phonenumber
            with open("khachhang.json", "w") as file:
                json.dump(customers, file, indent=4)
                mb.showinfo("Thành Công", "Thông tin khách hàng đã được cập nhật.")
            edit_window.destroy()
            refresh_listbox()

        tk.Button(edit_window, text="Lưu", command=save_changes, font="Times 20").grid(
            row=4, columnspan=2
        )
        tk.Button(
            edit_window, text="Thoát", command=edit_window.destroy, font="Times 20"
        ).grid(row=5, columnspan=2)

    def Find():
        add_window = tk.Toplevel(root)
        add_window.title("Tìm kiếm Khách Hàng")
        tk.Label(add_window, text="Nhập tên khách hàng cần tìm:", font="Times 20").grid(
            row=0, column=0
        )
        name_entry = tk.Entry(
            add_window, width=30, font="Times 20", insertbackground="red"
        )
        name_entry.focus()
        name_entry.grid(row=0, column=1)

        def search_customer():
            name_to_find = name_entry.get()
            if not name_to_find:
                mb.showwarning("Lỗi", "Hãy nhập tên khách hàng.")
                return
            with open("khachhang.json", "r", encoding="utf-8") as file:
                customers = json.load(file)
            found = False
            for index, customer in enumerate(customers):
                if customer["Name"].lower() == name_to_find.lower():
                    list.selection_clear(0, tk.END)
                    list.selection_set(index)
                    list.see(index)
                    found = True
                    mb.showinfo(
                        "Thành Công", f"Đã tìm thấy khách hàng: {customer['Name']}"
                    )
                    add_window.destroy()
                    break
            if not found:
                mb.showwarning(
                    "Không tìm thấy", "Không tìm thấy khách hàng với tên đã nhập."
                )
                add_window.destroy()

        tk.Button(
            add_window, text="Tìm", font="Times 20", command=search_customer
        ).grid(row=4, columnspan=2)
        tk.Button(
            add_window, text="Thoát", font="Times 20", command=add_window.destroy
        ).grid(row=5, columnspan=2)

    ##########################################################################################
    reset_config()
    root.title("QLKH (Admin)")
    root["bg"] = "dimgray"
    label = tk.Label(
        text="Quản lý khách hàng (Admin)",
        font="Times 30 bold",
        bg="cornsilk",
        anchor="center",
    )
    label.grid(row=0, columnspan=4)
    list = tk.Listbox(
        root,
        height=7,
        selectmode=tk.SINGLE,
        activestyle="dotbox",
        width=60,
        font="Times 20",
        bg="silver",
    )
    list.grid(row=1, columnspan=4, sticky="n")
    scrollbar = tk.Scrollbar(root, orient=tk.VERTICAL, command=list.yview)
    scrollbar.grid(row=1, column=4, sticky="ns")
    list.config(yscrollcommand=scrollbar.set)
    try:
        with open("khachhang.json", "r", encoding="utf-8") as file:
            customers = json.load(file)
    except FileNotFoundError:
        customers = []
    for i in customers:
        list.insert(tk.END, i["Name"])

    ###########################################################

    Creat_KH_button = tk.Button(
        root,
        text="Thêm 1 Khách hàng",
        font="Times 15",
        width=30,
        wraplength=200,
        command=Creat,
        bg="silver",
        relief="raise",
        activebackground="darkorange",
    )
    Creat_KH_button.grid(row=3, column=0, padx=10, sticky="news", pady=10)

    Update_KH_button = tk.Button(
        root,
        text="Sửa 1 Khách hàng",
        font="Times 15",
        width=30,
        wraplength=200,
        command=edit,
        bg="silver",
        relief="raise",
        activebackground="darkorchid",
    )
    Update_KH_button.grid(row=3, column=1, padx=10, sticky="news", pady=10)

    Find_KH_button = tk.Button(
        root,
        text="Tìm kiếm Khách hàng",
        font="Times 15",
        width=30,
        wraplength=200,
        command=Find,
        bg="silver",
        relief="raise",
        activebackground="darkred",
    )
    Find_KH_button.grid(row=3, column=2, padx=10, sticky="news", pady=10)
    Detail_KHKH_button = tk.Button(
        root,
        text="Xem thông tin chi tiết",
        font="Times 15",
        width=30,
        wraplength=200,
        command=detail,
        bg="silver",
        relief="raise",
        activebackground="darksalmon",
    )
    Detail_KHKH_button.grid(row=3, column=3, padx=10, sticky="news", pady=10)
    Remove_KHbutton = tk.Button(
        root,
        text="Xóa 1 Khách hàng khỏi danh sách",
        font="Times 15 underline",
        width=30,
        wraplength=200,
        command=delete,
        bg="silver",
        relief="raise",
        activebackground="darkseagreen",
    )
    Remove_KHbutton.grid(
        row=4, column=0, padx=10, sticky="news", pady=10, ipadx=5, columnspan=2
    )
    Crawl_DL_button = tk.Button(
        root,
        text="Crawl dữ liệu từ website",
        font="Times 15 underline",
        width=30,
        wraplength=200,
        command=crawl,
        bg="silver",
        relief="raise",
        activebackground="darkslategray",
    )
    Crawl_DL_button.grid(row=4, column=2, columnspan=2, padx=10, sticky="news", pady=10)

    back_login_button = tk.Button(
        root, text="Đăng xuất", font="Times 20", command=LoginPanel
    )
    back_login_button.grid(row=5, columnspan=4)
    root.rowconfigure(0, weight=1)
    root.rowconfigure(1, weight=1)
    root.rowconfigure(2, weight=1)
    root.rowconfigure(3, weight=1)
    root.rowconfigure(4, weight=1)
    root.rowconfigure(5, weight=1)
    root.columnconfigure(0, weight=1)
    root.columnconfigure(1, weight=1)
    root.columnconfigure(2, weight=1)
    root.columnconfigure(3, weight=1)


def UserPanel():
    def refresh_listbox():
        list.delete(0, tk.END)  # Clear the listbox
        try:
            with open("khachhang.json", "r", encoding="utf-8") as file:
                customers = json.load(file)
            for customer in customers:
                # In ra tên khách hàng để kiểm tra
                print(customer["Name"])
                list.insert(tk.END, customer["Name"])
        except FileNotFoundError:
            mb.showerror("Lỗi", "Không tìm thấy file khachhang.json.")
        except json.JSONDecodeError:
            mb.showerror("Lỗi", "Nội dung tệp khachhang.json không hợp lệ.")
        except KeyError as e:
            mb.showerror("Lỗi", f"Không tìm thấy khóa trong dữ liệu JSON: {e}")
        except Exception as e:
            mb.showerror("Lỗi", f"Đã xảy ra lỗi: {e}")

    def detail():
        selected_index = list.curselection()
        if not selected_index:
            mb.showwarning(
                "Chọn Khách Hàng", "Hãy chọn một khách hàng để xem chi tiết khách hàng."
            )
            return
        index = selected_index[0]
        with open("khachhang.json", "r", encoding="utf-8") as file:
            customers = json.load(file)
        selected_customer = customers[index]
        mb.showinfo(
            "Thông tin chi tiết",
            f"Họ Tên: {selected_customer['Name']}\nEmail: {selected_customer['Email']}\nĐịa chỉ: {selected_customer['Address']}\nSố điện thoại: {selected_customer['Phonenumber']}",
        )

    def Creat():
        def create_customer():
            name = name_entry.get()
            email = email_entry.get()
            address = address_entry.get()
            phonenumber = phonenumber_entry.get()
            try:
                with open("khachhang.json", "r", encoding="utf-8") as file:
                    customers = json.load(file)
            except FileNotFoundError:
                customers = []
            if not (name and email and address and phonenumber):
                mb.showwarning("Lỗi", "Tất cả các trường phải được điền đầy đủ.")
                add_window.destroy()
                return
            if not is_valid_email(email) or not is_valid_phonenumber(phonenumber):
                mb.showwarning("Lỗi", "Email hoặc số điện thoại không đúng định dạng")
                add_window.destroy()
                return
            if not duplicate_phone_email_for_KH(phonenumber, email):
                mb.showwarning("Lỗi", "Email hoặc số điện thoại đã tồn tại")
                add_window.destroy()
                return
            new_KH = Khachhang(name, address, phonenumber, email)
            customers.append(new_KH.__dict__)
            with open("khachhang.json", "w", encoding="utf-8") as file:
                json.dump(customers, file, indent=4, ensure_ascii=False)
                mb.showinfo("Thành Công", "Khách hàng mới đã được thêm.")
            refresh_listbox()
            add_window.destroy()

        add_window = tk.Toplevel(root)
        add_window.title("Thêm Khách Hàng")
        tk.Label(add_window, text="Tên:", font="Times 20").grid(row=0, column=0)
        tk.Label(add_window, text="Email:", font="Times 20").grid(row=1, column=0)
        tk.Label(add_window, text="Địa chỉ:", font="Times 20").grid(row=2, column=0)
        tk.Label(add_window, text="Số điện thoại:", font="Times 20").grid(
            row=3, column=0
        )

        name_entry = tk.Entry(add_window, width=30, font="Times 20")
        name_entry.focus()
        email_entry = tk.Entry(add_window, width=30, font="Times 20")
        address_entry = tk.Entry(add_window, width=30, font="Times 20")
        phonenumber_entry = tk.Entry(add_window, width=30, font="Times 20")

        name_entry.grid(row=0, column=1)
        email_entry.grid(row=1, column=1)
        address_entry.grid(row=2, column=1)
        phonenumber_entry.grid(row=3, column=1)
        tk.Button(
            add_window, text="Tạo", command=create_customer, font="Times 20"
        ).grid(row=4, columnspan=2)
        tk.Button(
            add_window, text="Thoát", command=add_window.destroy, font="Times 20"
        ).grid(row=5, columnspan=2)

    def edit():
        selected_item = list.curselection()
        if not selected_item:
            mb.showwarning("Chọn Khách Hàng", "Hãy chọn một khách hàng để sửa.")
            return
        index = selected_item[0]
        with open("khachhang.json", "r", encoding="utf-8") as file:
            customers = json.load(file)
        customer = customers[index]
        edit_window = tk.Toplevel(root)
        edit_window.title("Sửa Khách Hàng")

        tk.Label(edit_window, text="Tên:", font="Times 20").grid(row=0, column=0)
        tk.Label(edit_window, text="Email:", font="Times 20").grid(row=1, column=0)
        tk.Label(edit_window, text="Địa chỉ:", font="Times 20").grid(row=2, column=0)
        tk.Label(edit_window, text="Số điện thoại:", font="Times 20").grid(
            row=3, column=0
        )

        name_entry = tk.Entry(edit_window, font="Times 20", width=50)
        name_entry.focus()
        name_entry.insert(0, customer["Name"])
        email_entry = tk.Entry(edit_window, font="Times 20", width=50)
        email_entry.insert(0, customer["Email"])
        address_entry = tk.Entry(edit_window, font="Times 20", width=50)
        address_entry.insert(0, customer["Address"])
        phonenumber_entry = tk.Entry(edit_window, font="Times 20", width=50)
        phonenumber_entry.insert(0, customer["Phonenumber"])

        name_entry.grid(row=0, column=1)
        email_entry.grid(row=1, column=1)
        address_entry.grid(row=2, column=1)
        phonenumber_entry.grid(row=3, column=1)

        def is_duplicate(email, phone):
            for i, cust in enumerate(customers):
                if i != index and (
                    cust["Email"] == email or cust["Phonenumber"] == phone
                ):
                    return True
            return False

        def save_changes():
            # Update customer information with new data
            new_name = name_entry.get()
            new_email = email_entry.get()
            new_address = address_entry.get()
            new_phonenumber = phonenumber_entry.get()

            if not is_valid_email(
                customers[index]["Email"]
            ) or not is_valid_phonenumber(customers[index]["Phonenumber"]):
                mb.showwarning("Lỗi", "Email hoặc số điện thoại không đúng định dạng")
                edit_window.destroy()
                return
            if is_duplicate(new_email, new_phonenumber):
                mb.showwarning("Lỗi", "Email hoặc số điện thoại đã tồn tại")
                edit_window.destroy()
                return
            customers[index]["Name"] = new_name
            customers[index]["Email"] = new_email
            customers[index]["Address"] = new_address
            customers[index]["Phonenumber"] = new_phonenumber
            with open("khachhang.json", "w") as file:
                json.dump(customers, file, indent=4)
                mb.showinfo("Thành Công", "Thông tin khách hàng đã được cập nhật.")
            edit_window.destroy()
            refresh_listbox()

        tk.Button(
            edit_window, text="Lưu", command=save_changes, font="Times 20", fg="red"
        ).grid(row=4, columnspan=2)
        tk.Button(
            edit_window, text="Thoát", command=edit_window.destroy, font="Times 20"
        ).grid(row=5, columnspan=2)

    def Find():
        add_window = tk.Toplevel(root)
        add_window.title("Tìm kiếm Khách Hàng")
        tk.Label(add_window, text="Nhập tên khách hàng cần tìm:", font="Times 20").grid(
            row=0, column=0
        )
        name_entry = tk.Entry(add_window, width=30, font="Times 20")
        name_entry.focus()
        name_entry.grid(row=0, column=1)
        def search_customer():
            name_to_find = name_entry.get()
            if not name_to_find:
                mb.showwarning("Lỗi", "Hãy nhập tên khách hàng.")
                return
            with open("khachhang.json", "r", encoding="utf-8") as file:
                customers = json.load(file)
            found = False
            for index, customer in enumerate(customers):
                if customer["Name"].lower() == name_to_find.lower():
                    list.selection_clear(0, tk.END)
                    list.selection_set(index)
                    list.see(index)
                    found = True
                    mb.showinfo(
                        "Thành Công", f"Đã tìm thấy khách hàng: {customer['Name']}"
                    )
                    add_window.destroy()
                    break
            if not found:
                mb.showwarning(
                    "Không tìm thấy", "Không tìm thấy khách hàng với tên đã nhập."
                )
                add_window.destroy()
        tk.Button(
            add_window, text="Tìm", font="Times 20", command=search_customer
        ).grid(row=4, columnspan=2)
        tk.Button(
            add_window, text="Thoát", font="Times 20", command=add_window.destroy
        ).grid(row=5, columnspan=2)

    ##########################################################################################
    root.title("QLKH (User)")
    root["bg"] = "lavender"
    reset_config()
    label = tk.Label(
        text="Quản lý khách hàng (User)",
        font="Times 30 bold",
        bg="red",
        anchor="center",
    )
    label.grid(row=0, columnspan=4)
    list = tk.Listbox(
        root,
        height=7,
        selectmode=tk.SINGLE,
        activestyle="dotbox",
        width=60,
        font="Times 20",
    )
    list.grid(row=1, columnspan=4, sticky="n")
    scrollbar = tk.Scrollbar(root, orient=tk.VERTICAL, command=list.yview)
    scrollbar.grid(row=1, column=4, sticky="ns")
    list.config(yscrollcommand=scrollbar.set)
    try:
        with open("khachhang.json", "r", encoding="utf-8") as file:
            customers = json.load(file)
    except FileNotFoundError:
        customers = []
    for i in customers:
        list.insert(tk.END, i["Name"])

    ###########################################################

    Creat_KH_button = tk.Button(
        root,
        text="Thêm 1 Khách hàng",
        font="Times 15",
        width=30,
        wraplength=200,
        command=Creat,
        relief="groove",
        borderwidth=5,
    )
    Creat_KH_button.grid(row=3, column=0, padx=10, sticky="news", pady=10)

    Update_KH_button = tk.Button(
        root,
        text="Sửa 1 Khách hàng",
        font="Times 15",
        width=30,
        wraplength=200,
        command=edit,
        relief="groove",
        borderwidth=5,
    )
    Update_KH_button.grid(row=3, column=1, padx=10, sticky="news", pady=10)

    Find_KH_button = tk.Button(
        root,
        text="Tìm kiếm Khách hàng",
        font="Times 15",
        width=30,
        wraplength=200,
        command=Find,
        relief="groove",
        borderwidth=5,
    )
    Find_KH_button.grid(row=3, column=2, padx=10, sticky="news", pady=10)
    Detail_KHKH_button = tk.Button(
        root,
        text="Xem thông tin chi tiết",
        font="Times 15",
        width=30,
        wraplength=200,
        command=detail,
        relief="groove",
        borderwidth=5,
    )
    Detail_KHKH_button.grid(row=3, column=3, padx=10, sticky="news", pady=10)
    Remove_KHbutton = tk.Button(
        root,
        text="Xóa 1 Khách hàng khỏi danh sách",
        font="Times 15",
        width=30,
        wraplength=200,
        state="disabled",
        relief="groove",
        borderwidth=5,
    )
    Remove_KHbutton.grid(
        row=4, column=0, padx=10, sticky="news", pady=10, ipadx=5, columnspan=2
    )
    Crawl_DL_button = tk.Button(
        root,
        text="Crawl dữ liệu từ website",
        font="Times 15",
        width=30,
        wraplength=200,
        state="disabled",
        relief="groove",
        borderwidth=5,
    )
    Crawl_DL_button.grid(row=4, column=2, columnspan=2, padx=10, sticky="news", pady=10)

    back_login_button = tk.Button(
        root, text="Đăng xuất", font="Times 20", command=LoginPanel
    )
    back_login_button.grid(row=5, columnspan=4)
    root.rowconfigure(0, weight=1)
    root.rowconfigure(1, weight=1)
    root.rowconfigure(2, weight=1)
    root.rowconfigure(3, weight=1)
    root.rowconfigure(4, weight=1)
    root.rowconfigure(5, weight=1)
    root.columnconfigure(0, weight=1)
    root.columnconfigure(1, weight=1)
    root.columnconfigure(2, weight=1)
    root.columnconfigure(3, weight=1)


def RegisterPanel():
    def get_registration_data():
        if (
            fullname_entry.get() == ""
            or Email_entry.get() == ""
            or address_entry.get() == ""
            or Phone_entry.get() == ""
            or username_entry.get() == ""
            or password_entry.get() == ""
            or radio_var.get() == ""
        ):
            return -1
        if not is_valid_phonenumber(Phone_entry.get()) or not is_valid_email(
            Email_entry.get()
        ):
            return -2
        if not duplicate_phone_email_for_user(
            Phone_entry.get(), Email_entry.get(), radio_var.get()
        ):
            return -3

        name = fullname_entry.get()
        email = Email_entry.get()
        address = address_entry.get()
        phonenumber = Phone_entry.get()
        username = username_entry.get()
        password = password_entry.get()
        role = radio_var.get()

        return User(name, address, phonenumber, email, username, password, role)

    def save():
        user_data = get_registration_data()
        if user_data == -1:
            mb.showwarning("Lỗi", "Vui lòng nhập đầy đủ thông tin")
        elif user_data == -2:
            mb.showwarning("Lỗi", "Email hoặc số điện thoại không đúng định dạng")
        elif user_data == -3:
            mb.showwarning("Lỗi", "Email hoặc số điện thoại đã tồn tại")
        else:
            file_path = "account.json"
            try:
                if os.stat(file_path).st_size == 0:
                    users = []
                else:
                    with open(file_path, "r", encoding="utf-8") as file:
                        users = json.load(file)
            except FileNotFoundError:
                users = []
            users.append(user_data.__dict__)
            with open(file_path, "w", encoding="utf-8") as file:
                json.dump(users, file, ensure_ascii=False, indent=4)
                mb.showinfo(
                    "Đăng ký thành công", "Thông tin của bạn đã được lưu thành công!"
                )
                clear_entries()

    def clear_entries():
        fullname_entry.delete(0, tk.END)
        Email_entry.delete(0, tk.END)
        address_entry.delete(0, tk.END)
        Phone_entry.delete(0, tk.END)
        username_entry.delete(0, tk.END)
        password_entry.delete(0, tk.END)
        radio_var.set("")

    #########################################################
    reset_config()
    root.title("Đăng ký")  # dat title
    root.columnconfigure(0, weight=1)
    root.columnconfigure(1, weight=1)
    root.columnconfigure(2, weight=1)
    root.rowconfigure(0, weight=2)
    root.rowconfigure(1, weight=2)
    root.rowconfigure(2, weight=2)
    root.rowconfigure(3, weight=1)
    root.rowconfigure(4, weight=1)
    root.rowconfigure(5, weight=1)
    root.rowconfigure(6, weight=1)
    root.rowconfigure(7, weight=1)
    root.rowconfigure(8, weight=1)
    root.rowconfigure(9, weight=1)
    root.configure(bg="palegoldenrod", borderwidth=10, relief="ridge")
    label = tk.Label(
        text="Đăng ký tài khoản",
        font="Times 30 bold",
        bg="palegoldenrod",
        anchor="center",
    )
    label.grid(row=0, columnspan=3, sticky="news")
    ##########
    role_label = tk.Label(root, text="Vai trò:", font="Times 20", bg="palegoldenrod")
    role_label.grid(row=1, column=0, sticky="w")
    username_label = tk.Label(
        root, text="Tên đăng nhập:", font="Times 20", bg="palegoldenrod"
    )
    username_label.grid(row=2, column=0, sticky="w")
    password_label = tk.Label(
        root, text="Mật khẩu:", font="Times 20", bg="palegoldenrod"
    )
    password_label.grid(row=3, sticky="w")
    name_label = tk.Label(root, text="Họ Tên:", font="Times 20", bg="palegoldenrod")
    name_label.grid(row=4, column=0, sticky="w")
    address_label = tk.Label(root, text="Địa chỉ:", font="Times 20", bg="palegoldenrod")
    address_label.grid(row=5, column=0, sticky="w")
    phone_label = tk.Label(
        root, text="Số điện thoại:", font="Times 20", bg="palegoldenrod"
    )
    phone_label.grid(row=6, column=0, sticky="w")
    email_label = tk.Label(root, text="Email:", font="Times 20", bg="palegoldenrod")
    email_label.grid(row=7, column=0, sticky="w")
    ################################################################################
    radio_var = tk.StringVar(value="")
    user = tk.Radiobutton(
        root,
        text="Người dùng",
        variable=radio_var,
        value="user",
        font="Times 20",
        relief="raised",
        activebackground="gray",
        activeforeground="orangered",
    )
    user.grid(row=1, column=1, sticky="news")
    admin = tk.Radiobutton(
        root,
        text="Quản trị viên",
        variable=radio_var,
        value="admin",
        font="Times 20",
        relief="raised",
        activebackground="gray",
        activeforeground="orangered",
    )
    admin.grid(row=1, column=2, sticky="news")
    username_entry = tk.Entry(root, width=60, font="Times 15")
    username_entry.focus()
    username_entry.grid(
        row=2, columnspan=2, column=1, pady=5, padx=(0, 10), sticky="news"
    )
    password_entry = tk.Entry(root, width=60, font="Times 15", show="*")
    password_entry.grid(
        row=3, columnspan=2, column=1, pady=5, padx=(0, 10), sticky="news"
    )
    fullname_entry = tk.Entry(root, width=60, font="Times 15")
    fullname_entry.grid(
        row=4, columnspan=2, column=1, pady=5, padx=(0, 10), sticky="news"
    )
    address_entry = tk.Entry(root, width=60, font="Times 15")
    address_entry.grid(
        row=5, columnspan=2, column=1, pady=5, padx=(0, 10), sticky="news"
    )
    Phone_entry = tk.Entry(root, width=60, font="Times 15")
    Phone_entry.grid(row=6, columnspan=2, column=1, pady=5, padx=(0, 10), sticky="news")
    Email_entry = tk.Entry(root, width=60, font="Times 15")
    Email_entry.grid(row=7, columnspan=2, column=1, pady=5, padx=(0, 10), sticky="news")
    ######################################################################
    register_button = tk.Button(root, text="Đăng ký", font=("Times 20"), command=save)
    register_button.grid(row=8, columnspan=3, ipadx=30, ipady=30)
    back_login_button = tk.Button(
        root, text="Quay lại trang đăng nhập", font="Times 20", command=LoginPanel
    )
    back_login_button.grid(row=9, columnspan=3, ipadx=30, ipady=30)
    quit_button = tk.Button(
        root,
        text="Close Program",
        command=root.destroy,
        background="red",
        font="Times 20 underline",
    )
    quit_button.grid(row=10, columnspan=3, ipadx=30, ipady=30, sticky="ne")


def check_login_admin():
    username = username_entry.get()
    password = password_entry.get()
    flag = False
    try:
        with open("account.json", "r", encoding="utf-8") as f:
            users = json.load(f)
            if users == []:
                raise FileNotFoundError
        for user in users:
            if (
                user["Password"] == password
                and user["Username"] == username
                and user["Role"] == "admin"
            ):
                role = user["Role"]
                mb.showinfo("Login Successful", f"Welcome, {username}")
                flag = True
                main_window(role)
        if not flag:
            raise FileNotFoundError
    except FileNotFoundError as e:
        mb.showerror("Login Failed", "Tên đăng nhập hoặc tài khoản không đúng")
    except json.JSONDecodeError:
        mb.showerror("Login Failed", "Tên đăng nhập hoặc tài khoản không đúng")


def check_login_user():
    username = username_entry.get()
    password = password_entry.get()
    flag = False
    try:
        with open("account.json", "r", encoding="utf-8") as f:
            users = json.load(f)
            if users == []:
                raise FileNotFoundError
        for user in users:
            if (
                user["Password"] == password
                and user["Username"] == username
                and user["Role"] == "user"
            ):
                role = user["Role"]
                mb.showinfo("Login Successful", f"Welcome, {username}")
                flag = True
                main_window(role)
        if not flag:
            raise FileNotFoundError
    except FileNotFoundError as e:
        mb.showerror("Login Failed", "Tên đăng nhập hoặc tài khoản không đúng")
    except json.JSONDecodeError:
        mb.showerror("Login Failed", "Tên đăng nhập hoặc tài khoản không đúng")


def main_window(role):
    for widget in root.winfo_children():
        widget.destroy()
    if role == "admin":
        AdminPanel()
    elif role == "user":
        UserPanel()


def LoginPanel():
    reset_config()
    root.title("Đăng nhập")  # dat title
    root.configure(bg="papayawhip", borderwidth=10, relief="ridge")
    label = tk.Label(
        text="Ứng dụng quản lý khách hàng",
        font="'Modern 30 bold",
        bg="papayawhip",
        fg="mediumvioletred",
    )
    label.grid(row=0, columnspan=4)
    label = tk.Label(text="Đăng Nhập", font="Calibri 30 bold", bg="papayawhip")
    label.grid(row=1, columnspan=4)
    username_label = tk.Label(root, text="Username:", font="Times 30", bg="papayawhip")
    username_label.grid(row=2, column=0, columnspan=2, sticky="e")
    global username_entry
    username_entry = tk.Entry(root, font="Times 30")
    username_entry.focus()
    username_entry.grid(row=2, column=2, columnspan=2, sticky="w")

    password_label = tk.Label(root, text="Password:", font="Times 30", bg="papayawhip")
    password_label.grid(row=3, column=0, columnspan=2, sticky="e")
    global password_entry
    password_entry = tk.Entry(root, show="*", font="Times 30")
    password_entry.grid(row=3, column=2, columnspan=2, sticky="w")

    login_button = tk.Button(
        root, text="Login as User", font="Times 20", command=check_login_user
    )
    login_button.grid(row=4, column=0, columnspan=2, sticky="e", ipadx=30, ipady=30)
    login_admin_button = tk.Button(
        root, text="Login as Administrator", font="Times 20", command=check_login_admin
    )
    login_admin_button.grid(
        row=4, column=2, columnspan=2, sticky="w", ipadx=30, ipady=30
    )
    registrer = tk.Button(root, text="Sign up", font="Times 20", command=RegisterPanel)
    registrer.grid(row=5, columnspan=4, ipadx=90, ipady=30)

    quit_button = tk.Button(
        root,
        text="Close program",
        command=root.destroy,
        background="red",
        font="Times 20 underline",
    )
    quit_button.grid(row=6, columnspan=4, ipadx=30, ipady=30, sticky="ne")
    root.columnconfigure(0, weight=2)
    root.columnconfigure(1, weight=1)
    root.columnconfigure(2, weight=1)
    root.columnconfigure(3, weight=1)
    root.rowconfigure(0, weight=2)
    root.rowconfigure(1, weight=2)
    root.rowconfigure(2, weight=2)
    root.rowconfigure(3, weight=1)
    root.rowconfigure(4, weight=1)
    root.rowconfigure(5, weight=1)
    root.rowconfigure(6, weight=1)


root = tk.Tk()
root.resizable(1, 1)  # false false: cann't change
root.geometry("1000x700+100+0")  # size ban dau`num3:truc X num4:trucY
LoginPanel()
root.mainloop()
