import matplotlib.pyplot as plt

# #########################################################################
# x = [1, 2, 3, 4, 5]
# y = [2, 3, 5, 7, 11]
# plt.plot(x, y)
# # add titles and labels
# plt.title("Line Plot")
# plt.xlabel("X-axis")
# plt.ylabel("Y-axis")

# # show the plot
# plt.show()
# #########################################################################
# sample data
# categories = ["A", "B", "C", "D"]
# values = [4, 7, 1, 8]

# create the bar chart
# plt.bar(categories, values)

# # add a title and labels
# plt.title("Bar Chart")
# plt.xlabel("Categories")
# plt.ylabel("Values")

# # show the plot
# plt.show()
# #########################################################################
# sample data
# x = [1, 2, 3, 4, 5]
# y = [10, 14, 12, 15, 20]

# # create the scatter plot
# plt.scatter(x, y)

# # add a title and labels
# plt.title("Scatter Plot")
# plt.xlabel("X-axis")
# plt.ylabel("Y-axis")

# # show the plot
# plt.show()
# #########################################################################
# sample data
# x1 = [1, 2, 3, 4, 5]
# y1 = [2, 3, 5, 7, 11]
# x2 = [1, 2, 3, 4, 5]
# y2 = [10, 14, 12, 15, 20]

# # create a figure with subplots
# fig, (ax1, ax2) = plt.subplots(1, 2)

# # first subplot
# ax1.plot(x1, y1)
# ax1.set_title("Plot 1")

# # second subplot
# ax2.plot(x2, y2)
# ax2.set_title("Plot 2")

# # show the plots
# plt.show()
# #########################################################################
# sample data
categories = ["A", "B", "C", "D"]
values = [4, 7, 1, 8]
x_axis = ["A", "B", "C", "D"]
y_axis = [4, 7, 1, 8]

# define custom colors and hatches for each bar
colors = ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728"]
hatches = ["/", "x", "\\", "*"]  # Different hatch patterns

# create the bar chart with custom colors and hatches
for i in range(len(categories)):
    plt.bar(categories[i], values[i], color=colors[i], hatch=hatches[i])

# add a title and labels
plt.title("Customized Bar Chart")
plt.xlabel("X-Axis")
plt.ylabel("Y-Axis")
plt.show()
