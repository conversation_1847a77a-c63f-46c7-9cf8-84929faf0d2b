# import csv
import json
import csv


def read_iris():
    # đọc file tiết kiệm bộ nhớ : file csv lớn
    with open("iris.csv", "r") as iris_file:
        irises = []
        headers = next(iris_file).strip().split(",")
        print(headers)
        for row in iris_file:
            iris = row.strip().split(",")
            iris_dict = dict(zip(headers, iris))
            irises.append(iris_dict)


def read_customer_data():
    with open("data.csv", "r", encoding="utf-8") as f:
        # for l in f:
        #     print(l.strip())
        # ============================
        csv_f = csv.DictReader(f)
        a = list(csv_f)
        print(json.dumps(a, indent=4))
        # ============================
        # read = csv.reader(f)
        # for i in read:
        #     print(i)


# read_customer_data()


def write_few_people():
    head = ["id", "name", "age"]
    student = [["sv001", "anna", 23], ["sv002", "bob", 22], ["sv003", "bdc", 24]]

    with open("three_people.csv", "w", encoding="utf-8", newline="") as f:
        csv_w = csv.writer(f)
        csv_w.writerow(head)
        csv_w.writerows(student)


def new():
    # with open("as.csv", "r") as csvfile:
    #     # skipinitialspace ~ strip
    #     reader = csv.reader(csvfile, skipinitialspace=True)
    #     for row in reader:
    #         print(row, type(row), len(row))

    # with open("as.csv", "r") as file:
    #     reader = csv.reader(file)
    #     for row in reader:
    #         print(row, type(row), len(row))

    with open("quote.csv", "r") as file:
        # QUOTE_MINIMAL = QUOTE_ALL
        reader = csv.reader(file, quoting=csv.QUOTE_MINIMAL, skipinitialspace=True)
        for row in reader:
            print(row)


# QUOTE_NONNUMERIC : doc duoc nhu vay
# "SN", "Name", "Quotes"
# 4, "khanh", "12 gio"

new()


# with open("as.csv", "r") as csvfile:
#     reader = csvfile.readlines()
#     for row in reader[1:]:
#         colum = row.strip().split(",")
#         print(colum[0], colum[1].strip())


def new2():

    csv.register_dialect(
        "myDialect", delimiter="|", skipinitialspace=True, quoting=csv.QUOTE_ALL
    )

    with open("off.csv", "r") as csvfile:
        reader = csv.reader(csvfile, dialect="myDialect")
        for row in reader:
            print(row)


def new3():
    with open("off.csv", "r") as csvfile:
        sample = csvfile.read(64)  # doc nhieu dong``
        has_header = csv.Sniffer().has_header(
            sample
        )  # cac gia tri cua cot khac nhau => true
        print(has_header)

        deduced_dialect = csv.Sniffer().sniff(sample)  # lấy cấu trúc , suy luận

    with open("off.csv", "r") as csvfile:
        reader = csv.reader(csvfile, deduced_dialect)
        for row in reader:
            print(row)


def write1():
    data_list = [
        ["SN", "Name", "Contribution"],
        [1, "Linus Torvalds", "Linux Kernel"],
        [2, "Tim Berners-Lee", "World Wide Web"],
        [3, "Guido van Rossum", "Python Programming"],
    ]
    with open("innovators.csv", "w", newline="") as file:
        writer = csv.writer(file, delimiter="|")
        writer.writerows(data_list)
        pass


def write2():
    row_list = [
        ["SN", "Name", "Quotes"],
        [1, "Buddha", "What we think we become"],
        [2, "Mark Twain", "Never regret anything that made you smile"],
        [3, "Oscar Wilde", "Be yourself everyone else is already taken"],
    ]
    with open("quotes.csv", "w", newline="") as file:
        writer = csv.writer(file, quoting=csv.QUOTE_STRINGS, delimiter="|")
        writer.writerows(row_list)


def write3():
    row_list = [
        ["SN", "Name", "Quotes"],
        [1, "Buddha", "What we think we become"],
        [2, "Mark Twain", "Never regret anything that made you smile"],
        [3, "Oscar Wilde", "Be yourself everyone else is already taken"],
    ]

    with open("quotes.csv", "w", newline="") as file:
        writer = csv.writer(
            file, quoting=csv.QUOTE_NONNUMERIC, delimiter=";", quotechar="*"
        )
        writer.writerows(row_list)


def dialect_write():

    row_list = [
        ["ID", "Name", "Email"],
        ["A878", "Alfonso K. Hamby", "<EMAIL>"],
        ["F854", "Susanne Briard", "<EMAIL>"],
        ["E833", "Katja Mauer", "<EMAIL>"],
    ]
    csv.register_dialect("myDialect", delimiter="|", quoting=csv.QUOTE_ALL)
    with open("office.csv", "w", newline="") as file:
        writer = csv.writer(file, dialect="myDialect")
        writer.writerows(row_list)


def dict_write():
    with open("players.csv", "w", newline="") as file:
        fieldnames = ["player_name", "fide_rating"]
        writer = csv.DictWriter(file, fieldnames=fieldnames)

        writer.writeheader()
        writer.writerow({"player_name": "Magnus Carlsen", "fide_rating": 2870})
        writer.writerow({"player_name": "Fabiano Caruana", "fide_rating": 2822})
        writer.writerow({"player_name": "Ding Liren", "fide_rating": 2801})


def escapechar():
    row_list = [
        ["Book", "Quote"],
        [
            "Lord of the Rings",
            '"All we have to decide is what to do with the time that is given us."',
        ],
        [
            "Harry Potter",
            '"It matters not what someone is born, but what they grow to be."',
        ],
    ]
    with open("book.csv", "w", newline="") as file:
        writer = csv.writer(
            file, escapechar="/", quoting=csv.QUOTE_NONE
        )  # dấu \ là tiền tố của tất cả dấu ',"
        writer.writerows(row_list)
