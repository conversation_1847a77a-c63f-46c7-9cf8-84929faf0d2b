import re


def split_into_sentences(
    paragraph="How old are u.  This is hold clowold gold!!   And oldshit oldfashion? folder golden",
):
    sentences = re.split(r"(?<=[.!?]) +", paragraph)  # dau cach co the nhieu lan
    print(sentences)


# split_into_sentences()


def replace_word(
    old_word="old",
    new_word="ERROR",
    paragraph="How old are u.  This is hold clowold gold!   And oldshit oldfashion? folder golden",
):
    replaced_text = re.sub(r"\b" + re.escape(old_word) + r"\b", new_word, paragraph)
    print(replaced_text)
    replaced_text2 = re.sub(old_word, new_word, paragraph)
    print(replaced_text2)


# replace_word()


def split_into_words(paragraph="This_is a?sample paragraph. It ttontains, sss words."):
    # Tìm tất cả các từ trong đoạn văn
    words = re.findall(r"\b\w{6,}\b", paragraph)
    print(words)


# split_into_words()


def find_words_with_a(
    paragraph="This is a sample paragraph containing words like apple, banana, and orange ago ag.",
):
    # Tìm tất cả các từ trong đoạn văn
    words_with_a = re.findall(r"\b\w*a\w*\b", paragraph)
    print(words_with_a)


# find_words_with_a()


def find_words_starting_with_capital(
    paragraph="This Is A saMple paRagraph. It containS A SeveraL wOrds Like applE, BananA, and OraNge.",
):
    words_list = re.findall(r"\b[A-Z]\w*\b", paragraph)
    print(words_list)


# find_words_starting_with_capital()


def find_words_ending_with_number(
    paragraph="This1 1s 3  345 an54d aDC 2corocol1 Ba3bebu YOU BEAUTIFU2L ",
):
    words_list = re.findall(r"\b\w*\d\b", paragraph)
    print(words_list)


# find_words_ending_with_number()


def find_words_at_least_5_characters(
    paragraph="This i@s a Sa,mp1le par2ag.raph. It3 5co#ntains Sev$eral w%ords 5l^ike Ap&ple, Bana(na8, an)d Orange.",
):
    words_list = re.findall(r"\b[\w!@#$%^&*()_+.]{5,}\b", paragraph)
    print(words_list)


# find_words_at_least_5_characters()


def split_into_sentences(
    paragraph="This is a sample ? paragraph.   It contains   ;;Several words ??like Apple, Banana! and Oran?ge.   What do you think?",
):
    sentences = re.split(r"[.!?;]+[\s]*", paragraph)
    # sentences = re.split(r"(?<=[.!?])+[\s]*", paragraph)
    print(sentences)


# split_into_sentences()


def find_numbers(
    paragraph="0 ,12 .345 ,4567! 7.89? 12.3456?? 123133.4!!  -1324. -1.123.165, -1321654.6546532 ;-1641651.2",
):
    numbers_list = re.findall(
        r"(?:\b|-|\.)\d+(?:.\d+)?\b", paragraph
    )  # ? cho 1 lan thap phan || * nhieu lan

    print(numbers_list)


# find_numbers()
txt = "asd     123     abc"
s = re.search(r"\s+", txt)
if s:
    print(s.start())


txt = "asd     123====abc"
f = re.split(r"=+", txt)
print(f)


txt = "$12.85"
pattern = r"\$(\d+.\d+)"
match = re.search(pattern, txt)
print(match.endpos)

txt = "ha\tasd\n123"
print(re.sub(r"\s+", "??", txt))
