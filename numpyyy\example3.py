import numpy as np
from pathlib import Path

# Định nghĩa các ngày trong tuần
days = ["mon", "tue", "wed", "thu", "fri"]
# Đ<PERSON>nh nghĩa kiểu dữ liệu cho các ngày
days_dtype = [(day, "f8") for day in days]
# Định nghĩa kiểu dữ liệu cho công ty
company_dtype = [("company", "U20"), ("sector", "U20")]

# Tạo kiểu dữ liệu cho mảng portfolio
portfolio_dtype = np.dtype(company_dtype + days_dtype)
# Tạo mảng portfolio rỗng
portfolio = np.zeros((6,), dtype=portfolio_dtype)

# print(portfolio)
# ///////////////////////////////////   //////////////////////////////
# Đ<PERSON><PERSON> dữ liệu từ portfolio.csv
companies = np.loadtxt(
    Path("portfolio.csv"),
    delimiter=",",
    dtype=company_dtype,
    skiprows=1,
).reshape((6,))

# Chèn dữ liệu vào mảng portfolio
portfolio[["company", "sector"]] = companies

# print(portfolio)
# ////////////////////////////////////////////////////////////////////////
share_prices_dtype = [("company", "U20"), ("day", "f8")]

# Nhập dữ liệu giá cổ phiếu
for day, csv_file in zip(days, sorted(Path.cwd().glob("share_prices-?.csv"))):
    portfolio[day] = np.loadtxt(
        csv_file.name,
        delimiter=",",
        dtype=share_prices_dtype,
        skiprows=1,
    )["day"]

# print(portfolio)
# # ////////////////////////////////////////////////////////////////////////
# # Lấy dữ liệu của Company_C
# print(portfolio[portfolio["company"] == "Company_C"])
# # ////////////////////////////////////////////////////////////////////////
# # Giá cổ phiếu thứ Sáu của các công ty công nghệ
# print(portfolio[portfolio["sector"] == "technology"]["fri"])
# # ////////////////////////////////////////////////////////////////////////
# # Tính giá trị cổ phiếu của 250 cổ phần trong các công ty công nghệ
# tech_values = portfolio[portfolio["sector"] == "technology"]["fri"] * 250 * 0.01
# print(tech_values)

# # Tổng giá trị
# total_value = sum(tech_values)
# print(total_value)
# ////////////////////////////////////////////////////////////////////////
import matplotlib.pyplot as plt

# Tạo mặt nạ cho các công ty công nghệ
tech_mask = portfolio["sector"] == "technology"
tech_sector = portfolio[tech_mask]["company"]
tech_valuation = portfolio[tech_mask]["fri"] * 250 * 0.01

# Vẽ biểu đồ
plt.bar(x=tech_sector, height=tech_valuation, color="g")
plt.xlabel("Tech Companies")
plt.ylabel("Friday Price ($)")
plt.title("Tech Share Valuation ($)")
# plt.show()
# ////////////////////////////////////////////////////////////////////////////
import numpy as np
from pathlib import Path

# Định nghĩa các thành phố
cities = ["london", "new_york", "rome"]
# Định nghĩa kiểu dữ liệu cho các thành phố
cities_dtype = [(city, "i8") for city in cities]
# Định nghĩa kiểu dữ liệu cho nhiệt độ
city_files_dtype = [("month", "U20"), ("temp", "i8")]
# Định nghĩa kiểu dữ liệu cho mảng weather_data
weather_data_dtype = np.dtype([("month", "U20")] + cities_dtype)

# Tạo mảng weather_data rỗng
weather_data = np.zeros((12,), dtype=weather_data_dtype)

# Nhập nhiệt độ cho từng thành phố
for city in cities:
    temps = np.loadtxt(
        Path(f"{city}_temperatures.csv"),
        delimiter=",",
        dtype=city_files_dtype,
    )
    weather_data[["month", city]] = temps

print(weather_data)
# ///////////////////////////////////////////////////////////////////////////
import matplotlib.pyplot as plt

# Vẽ đường cho nhiệt độ các thành phố
plt.plot(weather_data["month"], weather_data["london"], label="London")
plt.plot(weather_data["month"], weather_data["new_york"], label="New York")
plt.plot(weather_data["month"], weather_data["rome"], label="Rome")

# Thêm nhãn và tiêu đề
plt.ylabel("Temperature (C)")
plt.xlabel("Month")
plt.title("Average Monthly Temperatures")
plt.legend()
plt.show()
