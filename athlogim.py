def deadlock_example():
    import threading

    barrier = threading.Barrier(4)

    class thread(threading.Thread):
        def __init__(self, thread_ID, thread_name):
            threading.Thread.__init__(self)
            self.thread_ID = thread_ID
            self.thread_name = thread_name

        def run(self):
            print(
                "ThreadID = "
                + str(self.thread_ID)
                + ", ThreadName = "
                + self.thread_name
                + "\n"
            )
            try:
                barrier = threading.Barrier(4)
                barrier.wait()
            except:
                print("barrier broken")

    thread1 = thread(100, "GFG")
    thread2 = thread(101, "Geeks")
    thread3 = thread(102, "GeeksforGeeks")

    thread1.start()
    thread2.start()
    thread3.start()

    barrier.wait()

    print("Exit")


def check_valid_IP():

    def is_valid(ip):
        ip = ip.split(".")
        for i in ip:
            if (
                len(i) > 3
                or int(i) < 0
                or int(i) > 255
                or (len(i) > 1 and int(i) != 0 and i[0] == "0")
            ):
                return False
        return True

    def convert(s):
        sz = len(s)
        if sz > 12:
            return []
        snew = s
        l = []

        for i in range(1, sz - 2):  # 1 ..8
            for j in range(i + 1, sz - 1):  # 2 ...9
                for k in range(j + 1, sz):  # 3 ...10
                    snew = snew[:k] + "." + snew[k:]
                    snew = snew[:j] + "." + snew[j:]
                    snew = snew[:i] + "." + snew[i:]
                    # print(snew, end="\t")
                    if is_valid(snew):
                        l.append(snew)

                    snew = s
        return l

    A = "25525511135"
    B = "25505011535"
    print(convert(A))
    print(convert(B))


def allSame(input_str="xxxyyzz"):
    """một thao tác có thể làm tần suất xuất hiện bằng nhau"""
    # Tạo một dictionary để đếm số lần xuất hiện của mỗi ký tự
    freq = {}
    for char in input_str:
        if char in freq:
            freq[char] += 1
        else:
            freq[char] = 1
    print(freq)
    # Lấy tất cả các giá trị đếm từ dictionary và đưa vào một set
    freq_values = set(freq.values())
    print(freq_values)

    if len(freq_values) > 2:
        print("No")
    elif len(freq_values) == 2:  #
        max_freq = max(freq_values)
        min_freq = min(freq_values)
        print(max_freq, min_freq)
        print(list(freq.values()).count(max_freq), list(freq.values()).count(min_freq))
        # Kiểm tra điều kiện nếu có thể giảm một ký tự để làm cho các tần suất giống nhau
        if max_freq - min_freq > 1:
            print("No")
        elif (
            list(freq.values()).count(max_freq) > 1
            and list(freq.values()).count(min_freq) > 1
        ):
            print("No")
        else:
            print("Yes")
    else:
        print("Yes")


def differentSquares(matrix):
    if len(matrix) < 2 or len(matrix[0]) < 2:
        return 0

    squares = set()
    rows, cols = len(matrix), len(matrix[0])

    for i in range(rows - 1):
        for j in range(cols - 1):
            square = (
                matrix[i][j],
                matrix[i][j + 1],
                matrix[i + 1][j],
                matrix[i + 1][j + 1],
            )
            squares.add(square)

    return len(squares)

    matrix = [[1, 2, 1], [2, 2, 2], [2, 2, 2], [1, 2, 3], [2, 2, 1]]

    result = differentSquares(matrix)
    print(result)  # Output: 6


def spiralNumbers(n):
    """tạo hình xoắn ốc"""
    # Tạo ma trận n x n với các giá trị 0
    matrix = [[0] * n for _ in range(n)]

    # Các biến để theo dõi ranh giới
    left, right = 0, n - 1
    top, bottom = 0, n - 1
    num = 1

    while left <= right and top <= bottom:
        # Điền từ trái sang phải
        for i in range(left, right + 1):
            matrix[top][i] = num
            num += 1
        top += 1

        # Điền từ trên xuống dưới
        for i in range(top, bottom + 1):
            matrix[i][right] = num
            num += 1
        right -= 1

        if top <= bottom:
            # Điền từ phải sang trái
            for i in range(right, left - 1, -1):
                matrix[bottom][i] = num
                num += 1
            bottom -= 1

        if left <= right:
            # Điền từ dưới lên trên
            for i in range(bottom, top - 1, -1):
                matrix[i][left] = num
                num += 1
            left += 1

    return matrix


def climbingLeaderboard(ranked, player):
    """xếp hạng điểm"""
    # Remove duplicates from the ranked list and sort in descending order
    unique_ranked = sorted(set(ranked), reverse=True)
    print(unique_ranked)
    result = []
    n = len(unique_ranked)
    # Start from the lowest rank (end of the unique_ranked list)
    index = n - 1
    for score in player:
        # Move up the ranked list to find the appropriate rank
        while index >= 0 and score >= unique_ranked[index]:
            index -= 1
        # The rank is index + 2 because index + 1 would give us the position
        # immediately above the current index, and we need the next position
        result.append(index + 2)
    return result
    # C2
    result = []
    l = len(unique_ranked)
    for score in player:
        # Tìm kiếm vị trí chèn của điểm số vào danh sách thứ hạng
        while (l > 0) and (score >= unique_ranked[l - 1]):
            l -= 1
        result.append(l + 1)

    return result

    ranked = [100, 90, 90, 80, 75, 60]
    player = [50, 51, 52, 65, 77, 90, 102]
    print(climbingLeaderboard(ranked, player))


def sudokuChecking(grid):
    """check ma trận sudoku, không cần phải thiết phải giải
    mỗi hàng mỗi cột không trùng nhâu, mỗi ổ 3x3 không trùng nhau"""
    rows = [set() for _ in range(9)]
    cols = [set() for _ in range(9)]
    boxes = [set() for _ in range(9)]

    for i in range(9):
        for j in range(9):
            num = grid[i][j]
            if num != ".":
                box_index = (i // 3) * 3 + (j // 3)
                # print(box_index)
                if num in rows[i]:
                    return False
                rows[i].add(num)

                if num in cols[j]:
                    return False
                cols[j].add(num)

                if num in boxes[box_index]:
                    return False
                boxes[box_index].add(num)

    return True

    grid2 = [
        [".", ".", ".", "1", "4", ".", ".", "2", "."],
        [".", ".", "6", ".", ".", ".", ".", ".", "."],
        [".", ".", ".", ".", ".", ".", ".", ".", "."],
        [".", ".", "1", ".", ".", ".", ".", ".", "."],
        [".", "6", "7", ".", ".", ".", ".", ".", "9"],
        [".", ".", ".", ".", ".", ".", "8", "1", "."],
        [".", "3", ".", ".", ".", ".", ".", ".", "6"],
        [".", ".", ".", ".", ".", "7", ".", ".", "."],
        [".", ".", ".", "5", ".", ".", ".", "7", "."],
    ]

    print(sudokuChecking(grid2))  # Output: False


def findPath(matrix):
    """check đường đi liền kê trong ma trận"""
    if not matrix:
        return False
    rows, cols = len(matrix), len(matrix[0])
    num_positions = {}
    for i in range(rows):
        for j in range(cols):
            num_positions[matrix[i][j]] = (i, j)
    for current in range(1, rows * cols):
        if current not in num_positions or current + 1 not in num_positions:
            return False

        x1, y1 = num_positions[current]
        x2, y2 = num_positions[current + 1]

        if not ((x1 == x2 and abs(y1 - y2) == 1) or (y1 == y2 and abs(x1 - x2) == 1)):
            return False
    return True

    matrix1 = [[1, 4, 5], [2, 3, 6]]
    matrix2 = [[1, 3, 6], [2, 4, 5]]

    print(findPath(matrix1))  # Output: True
    print(findPath(matrix2))  # Output: False


def edit_distance(string1, string2):

    if len(string1) > len(string2):
        difference = len(string1) - len(string2)
        string1[:difference]

    elif len(string2) > len(string1):
        difference = len(string2) - len(string1)
        string2[:difference]

    else:
        difference = 0

    for i in range(len(string1)):
        if string1[i] != string2[i]:
            difference += 1

    return difference

    print(edit_distance("kitten", "sitting"))  # 3
    print(edit_distance("medium", "median"))  # 2


def prime_factors(n):
    """phân tích các thừa số nguyên tố"""
    i = 2
    factors = []
    while i * i <= n:
        if n % i:
            i += 1
        else:
            n //= i
            factors.append(i)
    if n > 1:
        factors.append(n)
    return factors

    numbers = [60, 1024, 825, 659246, 757820231]
    for number in numbers:
        print(f"Prime factors of {number}: {prime_factors(number)}")


def get_permutation(string, i=0):
    """hoán vị chữ (đệ quy)"""
    if i == len(string):
        print("".join(string))

    for j in range(i, len(string)):

        words = [c for c in string]

        # swap
        words[i], words[j] = words[j], words[i]

        get_permutation(words, i + 1)
    # import itertools
    # words = [p for p in itertools.permutations("pro")]


def power_set(s):
    # Khởi tạo power set với tập rỗng
    result = [[]]

    for element in s:
        # Tạo các tập con mới bằng cách thêm phần tử vào tất cả các tập con hiện tại
        result += [subset + [element] for subset in result]
        print(result)

    return result




# s = [1, 2, 3]
# print(power_set(s))  # Output: [[], [1], [2], [3], [1, 2], [1, 3], [2, 3], [1, 2, 3]]
