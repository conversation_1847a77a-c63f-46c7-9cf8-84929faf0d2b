from locator.book_locator import BookLocator


class BookParser:
    def __init__(self, parent):
        self.parent = parent

    @property
    def title(self):
        locator = BookLocator.TITLE
        return self.parent.select_one(locator).attrs["title"]

    @property
    def price(self):
        locator = BookLocator.PRICE
        return self.parent.select_one(locator).string

    @property
    def rating(self):
        locator = BookLocator.RATING
        # return self.parent.select_one(locator)["class"][1]
        return [
            e
            for e in self.parent.select_one(locator).attrs["class"]
            if e != "star-rating"
        ][0]

    def __str__(self):
        return f"<Book {self.title} and {self.price}, Rating: {self.rating}>"
