def get_val(num: str, kqua, op):
    """4 nếu không tham chiếu đệ quy , làm từ trên xuống dưới
    VALUE 3 _
    SUB $0 4
    ADD 8 $1
    MULT $0 $2
    -------------------------
    call(op(num1,num2,phep),index,kq)
    cong/tru/nhan: => rt(num1)+rt(num2)
    value: =>rt(num1)
    ------------------------
    rt(num,kq):
    if num start==$:
        ref-index=num[1:]
        if kq[ref-index] is none:
            pass
        => kq[ref-index]
    => int(num)
    -----------------------
    main:
    op(num11, num2, phep), index, kqua
    kq: 0 0 0 0
    for i in n:
    kq[i]=call(op,i,kq)"""
    if num.startswith("$"):
        ref_index = int(num[1:])
        if kqua[ref_index] is None:
            kqua[ref_index] = calculate(ref_index, kqua, op)
        return kqua[ref_index]
    else:
        return int(num)


def calculate(index, kqua, op: tuple):
    phep, numA, numB = op[index]
    if phep == "VALUE":
        return get_val(numA, kqua, op)
    elif phep == "ADD":
        return get_val(numA, kqua, op) + get_val(numB, kqua, op)
    elif phep == "SUB":
        return get_val(numA, kqua, op) - get_val(numB, kqua, op)
    elif phep == "MULT":
        return get_val(numA, kqua, op) * get_val(numB, kqua, op)


n = int(input())

operations = []
for _ in range(n):
    phep, numA, numB = input().split()
    operations.append((phep, numA, numB))


KQua = [None] * n
for i in range(n):
    if KQua[i] is None:
        KQua[i] = calculate(i, KQua, operations)

for value in KQua:
    print(value)
"""
6
MULT $5 $2
ADD $5 $0
VALUE 12 _
ADD $2 $2
MULT $3 $2
SUB $3 $2


4
SUB $1 4
VALUE 3 _
ADD 8 $1
MULT $0 $2


7
VALUE 10 _
VALUE 3 _
MULT $0 $1
VALUE 2 _
VALUE 4 _
MULT $3 $4
ADD $2 $5

"""
