from pathlib import Path
import numpy as np

# Tạo một mảng 3 chiều vớ<PERSON> kích thước (3, 2, 3)
array = np.zeros((3, 2, 3))

# Đ<PERSON><PERSON> dữ liệu từ các tệp CSV
for file_count, csv_file in enumerate(Path.cwd().glob("file?.csv")):
    array[file_count] = np.loadtxt(csv_file.name, delimiter=",")

# In ra hình dạng của mảng
print(array.shape)
print(array)
# ////////////////////////////////////////////////////////////////////////
# Tạo một mảng lớn hơn để chứa dữ liệu mới
array = np.zeros((4, 2, 3))

# Đ<PERSON><PERSON> dữ liệu từ các tệp CSV
for file_count, csv_file in enumerate(Path.cwd().glob("file?.csv")):
    array[file_count] = np.loadtxt(csv_file.name, delimiter=",")

# Thêm dữ liệu từ tệp ngắn
array[3, 0] = np.loadtxt("short_file.csv", delimiter=",")
# print(array)
# //////////////////////////////////////////////////////////////////////////////
array = np.zeros((4, 2, 3))

# Đọc dữ liệu từ các tệp CSV
for file_count, csv_file in enumerate(Path.cwd().glob("file?.csv")):
    array[file_count] = np.loadtxt(csv_file.name, delimiter=",")

# Thêm dữ liệu từ tệp dài
array = np.insert(arr=array, obj=2, values=0, axis=1)
array[3] = np.loadtxt("long_file.csv", delimiter=",")
print(array)
# ////////////////////////////////////////////////////////////////////////


array = np.zeros((3, 4, 4), dtype="i8")

array[0] = np.loadtxt("ex1a.csv", delimiter=",")

narrow_array = np.loadtxt("ex1b.csv", delimiter=",")
narrow_array = np.insert(arr=narrow_array, values=0, obj=3, axis=0)
array[1, 0] = narrow_array

short_array = np.loadtxt("ex1c.csv", delimiter=",")
short_array = np.insert(arr=short_array, values=0, obj=3, axis=0)
array[2, :, 0] = short_array

print(array)
