import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

houses = pd.read_csv("kc_house_data.csv")
movies = pd.read_csv("netflix_titles.csv", index_col=0)
titanic = pd.read_csv("titanic.csv")
bestsellers = pd.read_csv("bestsellers.csv")

# print(movies.min(numeric_only=True))
# print(movies.max(numeric_only=True))
# print(movies.count())
# print(movies.describe(include="all"))


# print(titanic.min(numeric_only=True))
# print(titanic.median(numeric_only=True))
# print(titanic.mode())
# print(titanic["name"].value_counts()) # print(titanic.name.value_counts())
# print(titanic.describe())
# print(titanic.columns)
# print(titanic.survived.unique())
# print(titanic.survived.nunique())
# print(titanic.survived.nlargest(20, keep="last"))
# print(titanic.survived.value_counts().plot(kind="line"))
# plt.show()

# print(houses.mean(numeric_only=True))
# print(houses["price"].mode())
# print(houses.bedrooms.value_counts().sort_index().plot(kind="bar"))
# plt.show()


# ==================================================BOOK==============================================
# print("min rating", bestsellers["User Rating"].min())
# print("max price", bestsellers["Price"].max())
# print("mean price", bestsellers["Price"].mean())
# print("avg rating of 5 rows", bestsellers["User Rating"].head().mean())
# print("most review appear", bestsellers["User Rating"].mode())
# print("sum review column", bestsellers["Reviews"].sum())
# print("differ author: ", bestsellers["Author"].describe()["unique"])
# print(
#     "author wrote most book:", bestsellers["Author"].value_counts().idxmax()
# )  # bestsellers["Author"].describe()["top"])

# print(bestsellers["Genre"].unique())
# print(bestsellers["Author"].nunique())
# print(bestsellers["Price"].nlargest(8))
# print(bestsellers["Name"].value_counts().head(3))
# new_df = bestsellers[["Author", "User Rating"]]
# print(new_df.value_counts())

# bestsellers.Genre.value_counts().plot(kind="pie")
# plt.show()
# num = bestsellers.Author.value_counts().head(10).plot(kind="barh")
# plt.show()

# bestsellers["User Rating"].plot(kind="box")
# plt.show()
# =======================================================================================================
