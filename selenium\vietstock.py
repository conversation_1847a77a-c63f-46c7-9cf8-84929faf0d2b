from selenium import webdriver
import time
from selenium.webdriver.common.by import By
from selenium.webdriver import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

browser = webdriver.Chrome()
browser.get("https://vietstock.vn/")
# /html/body/div[12]/div[5]/div[3]/div/div/div/a
# browser.find_element(By.XPATH, "/html/body/div[12]/div[5]/div[3]/div/div/div/a").click()

try:
    close_ad_button = WebDriverWait(browser, 5).until(
        EC.element_to_be_clickable(
            (By.XPATH, "/html/body/div[12]/div[5]/div[3]/div/div/div/a")
        )
    )
    close_ad_button.click()
except Exception as e:
    print(f"Không thể đóng quảng cáo: {e}")
time.sleep(5)
dn = browser.find_element(By.XPATH, "/html/body/div[9]/div[1]/div[2]/ul/li[3]/a")

ActionChains(browser).move_to_element(dn).perform()
time.sleep(5)
hdkd = browser.find_element(
    By.XPATH, "/html/body/div[7]/div/div[1]/div/div/div[1]/div/ul/li[4]/ul/li[1]/a"
)
hdkd.click()
# time.sleep(5)
# browser.quit()
