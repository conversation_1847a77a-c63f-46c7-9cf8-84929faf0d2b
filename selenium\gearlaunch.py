from selenium import webdriver
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select

# browser = webdriver.Chrome()
# browser.get("https://www.gearlaunch.com/shopify")
# browser.maximize_window()
# btn = browser.find_element(By.XPATH, "/html/body/div[4]/div[1]/div/div[2]/div/div[1]/a")
# href = btn.get_attribute("href")
# browser.switch_to.new_window()
# browser.get(href)


browser = webdriver.Chrome()
browser.maximize_window()
browser.get("https://sell.gearlaunch.com/login")
browser.implicitly_wait(5)
login_btn = browser.find_element(
    By.XPATH, "/html/body/div[1]/div/div[1]/div/div/div[2]/p/a"
)
login_btn.click()
print(len(browser.window_handles))
browser.switch_to.window(browser.window_handles[-1])
email = browser.find_element(
    By.XPATH,
    "/html/body/div[1]/div[1]/div[2]/c-wiz/div/div[2]/div/div/div[1]/form/span/section/div/div/div[1]/div/div[1]/div/div[1]/input",
)
email.send_keys("<EMAIL>")
btn_next = browser.find_element(
    By.XPATH,
    "/html/body/div[1]/div[1]/div[2]/c-wiz/div/div[3]/div/div[1]/div/div/button",
)
btn_next.click()

input()
