import sys


# Hàm xử lý các mô tả và lấy tập hợp các lá bài phù hợp
def get_matching_cards(description):
    suits = {"C", "D", "H", "S"}
    ranks = {"2", "3", "4", "5", "6", "7", "8", "9", "T", "J", "Q", "K", "A"}

    selected_ranks = set()
    selected_suits = set()

    for char in description:
        if char in ranks:
            selected_ranks.add(char)
        elif char in suits:
            selected_suits.add(char)

    if not selected_ranks:
        selected_ranks = ranks
    if not selected_suits:
        selected_suits = suits

    # Tạo tất cả các kết hợp rank-suit theo mô tả
    return {rank + suit for rank in selected_ranks for suit in selected_suits}


# Đọc dữ liệu đầu vào
r, s = map(int, input().split())
all_cards = {rank + suit for rank in "23456789TJQKA" for suit in "CDHS"}

# Xử lý các mô tả loại bỏ
for _ in range(r):
    removed_desc = input()
    all_cards -= get_matching_cards(removed_desc)

remaining_cards = all_cards  # Tập hợp các lá bài còn lại sau khi loại bỏ

# Xử lý các mô tả tìm kiếm
sought_cards = set()
for _ in range(s):
    sought_desc = input()
    sought_cards.update(get_matching_cards(sought_desc))

# Tìm số lượng lá phù hợp trong các lá bài còn lại
matching_cards = remaining_cards & sought_cards
probability = len(matching_cards) / len(remaining_cards) * 100

# In kết quả với xác suất làm tròn tới số nguyên gần nhất
print(f"{round(probability)}%")
"""
1 2
45C
7
H
"""
# https://www.codingame.com/ide/puzzle/playing-card-odds
