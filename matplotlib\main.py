from charts import create_chart
from charts import read_column

user_menu = """Please choose from the following options:

- Enter 'c' to chart a new graph.
- Enter 'q' to quit.

Your selection: """
filename_prompt = "Enter your desired file name: "
charting_menu = "Enter the column you'd like to chart: "


def handle_chart():
    column = int(input(charting_menu))
    # create_chart([1, 2, 3, 4, 5], [5.5, 6.4, 5.3, 4.4, 7.9])
    x = read_column(-1)
    y = [float(n) for n in read_column(column)]
    # y = map(float, read_column(column))
    filename = input(filename_prompt)
    create_chart(x, y, filename.strip())


while True:
    user_selection = input(user_menu)
    if user_selection == "q":
        break
    elif user_selection == "c":
        handle_chart()
    else:
        print(f"Sorry, '{user_selection}' is not a valid option.")
