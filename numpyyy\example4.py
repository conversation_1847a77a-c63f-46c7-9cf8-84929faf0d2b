import numpy as np
from pathlib import Path

# Đ<PERSON><PERSON> nghĩa kiểu dữ liệu cho các cột
share_dtypes = [
    ("company", "U20"),
    ("sector", "U20"),
    ("mon", "f8"),
    ("tue", "f8"),
    ("wed", "f8"),
    ("thu", "f8"),
    ("fri", "f8"),
]

# Đ<PERSON><PERSON> dữ liệu từ tệp CSV
portfolio = np.loadtxt(
    Path("full_portfolio.csv"),
    delimiter=",",
    dtype=share_dtypes,
    skiprows=1,
)


# Tính toán sự thay đổi giá cổ phiếu từ thứ Hai đến thứ Sáu
weekly_change = portfolio["fri"] - portfolio["mon"]
print(weekly_change)


# ////////////////////////////////////////////////////////////////////////////////////////////
def profit_with_bonus(first_day, last_day):
    if last_day >= first_day * 1.01:
        return (last_day - first_day) * 1.1
    else:
        return last_day - first_day


# profit_with_bonus(portfolio["mon"], portfolio["fri"])
# ////////////////////////////////////////////////////////////////////////////////////////////
vectorized_profit_with_bonus = np.vectorize(profit_with_bonus)
result = vectorized_profit_with_bonus(portfolio["mon"], portfolio["fri"])
print(result)


# // ////////////////////////////////////////////////////////////////////////////////////////////
@np.vectorize
def profit_with_bonus(first_day, last_day):
    if last_day >= first_day * 1.01:
        return (last_day - first_day) * 1.1
    else:
        return last_day - first_day


result = profit_with_bonus(portfolio["mon"], portfolio["fri"])
print(result)
# // ////////////////////////////////////////////////////////////////////////////////////////////
profit = np.where(
    portfolio["fri"] > portfolio["mon"] * 1.01,
    (portfolio["fri"] - portfolio["mon"]) * 1.1,
    portfolio["fri"] - portfolio["mon"],
)
print(profit)


# // /////////////////////////////////////////////////////////////////////////////////////////////
def find_min_max(first, second, third):
    min_temp = min(first, second, third)
    max_temp = max(first, second, third)
    return min_temp, max_temp


print(find_min_max(2, 1, 3))  # (1, 3)


# // /////////////////////////////////////////////////////////////////////////////////////////////
@np.vectorize
def find_min_max(first, second, third):
    min_temp = min(first, second, third)
    max_temp = max(first, second, third)
    return min_temp, max_temp


london_temps = weather_data["london"]
new_york_temps = weather_data["new_york"]
rome_temps = weather_data["rome"]

min_temps, max_temps = find_min_max(london_temps, new_york_temps, rome_temps)
print(min_temps)
print(max_temps)
