import tkinter as tk
import random
from tkinter import colorchooser
from tkinter import ttk
from tkinter import messagebox
from tkcalendar import DateEntry
import json
from tkinter import ttk
import calendar
import datetime


def hello():
    print("Hello!")


def quit_app():
    root.destroy()


def choose_color():
    color = colorchooser.askcolor(title="Chọn màu sắc")
    print("<PERSON><PERSON><PERSON> được chọn:", color)


def get_date():
    date = cal.get_date()
    messagebox.showinfo("Thông báo", f"<PERSON><PERSON><PERSON> được chọn: {date} va type: {type(date)}")


def add_child():
    selected_item = tree.focus()
    if selected_item:
        tree.insert(selected_item, "end", text="New Child", values=("Child Value"))


root = tk.Tk()
root.title("11")
root["bg"] = "#aeb0c1"
root.geometry("800x600+100+50")
# root.attributes("-topmost", True)
################################################################################
# lb1 = tk.Label(root, text="RED", bg="red").pack(side="top", fill="x")
# lb2 = tk.Label(root, text="BLUE", bg="blue").pack(side="left", fill="y")
# lb3 = tk.Label(root, text="GREEN", bg="green").pack(side="right", fill="both", expand=True)
# =============================================================================
#
# =============================================================================
# label4 = tk.Label(root, text="Label 4 ", bg="pink", fg="white")
# label4.grid(row=0, column=0,sticky="news")
# label4 = tk.Label(root, text="Label 41 ", bg="pink", fg="white")
# label4.grid(row=0, column=2,sticky="news")
# label4 = tk.Label(root, text="Label 42 ", bg="pink", fg="white")
# label4.grid(row=0, column=3,sticky="news")
# label4 = tk.Label(root, text="Label 43 ", bg="pink", fg="white")
# label4.grid(row=0, column=4,sticky="news")
# label4 = tk.Label(root, text="Label 44 ", bg="pink", fg="white")
# label4.grid(row=0, column=5,sticky="news")
# label4 = tk.Label(root, text="Label 45 ", bg="pink", fg="white")
# label4.grid(row=0, column=6,sticky="news")
# label5 = tk.Label(root, text="Label 5", bg="lightgreen", fg="white")
# label5.grid(row=1, column=0, sticky="ew")
# label6 = tk.Label(root, text="Label 6", bg="lightblue", fg="white")
# label6.grid(row=0, column=1, rowspan=4, sticky="news")

# root.columnconfigure(0, weight=1)
# root.columnconfigure(1, weight=1)
# root.columnconfigure(2, weight=1)
# root.columnconfigure(3, weight=1)
# root.columnconfigure(4, weight=1)
# root.columnconfigure(5, weight=1)
# root.rowconfigure(0, weight=5)
# root.rowconfigure(1, weight=1)
# root.rowconfigure(2, weight=1)
# root.rowconfigure(3, weight=1)
# =============================================================================
#
# =============================================================================

# label1 = tk.Label(root, text="Label 1 RED", bg="red", fg="white")
# label2 = tk.Label(root, text="Label GREEN", bg="green", fg="white")
# label3 = tk.Label(root, text="Label 3 BLUE", bg="blue", fg="white")
# label1.grid(row=0, column=0, sticky="ew")
# label2.grid(row=1, column=0, sticky="ns")
# label3.grid(row=0, column=1, rowspan=2, sticky="news")
# # Cấu hình cột và hàng để widget mở rộng
# root.columnconfigure(0, weight=1)
# root.columnconfigure(1, weight=2)
# root.rowconfigure(0, weight=1)
# root.rowconfigure(1, weight=1)
# =============================================================================
#
# # # =============================================================================
# label1 = tk.Label(root, text="1111111111", bg="red", fg="yellow")
# label2 = tk.Label(root, text="22222222", bg="green", fg="white")
# label3 = tk.Label(root, text="33333333333", bg="blue", fg="white")
# label1.place(
#     relx=0.5,
#     rely=0.5,
#     relwidth=0.4,
#     relheight=0.4,
#     bordermode="inside",
#     anchor="center",
# )
# # label1.place(x=50, y=50)

# label2.place(x=150, y=100, height=30)

# label3.place(x=180, y=130, width=100, height=20)
# =============================================================================
#
# # =============================================================================
# label1 = tk.Label(root, text="Label 1", bg="red", fg="white")
# label1.place(x=50, y=50, width=100, height=50, anchor="nw")
# label2 = tk.Label(root, text="Label 2", bg="green", fg="white")
# label2.place(relx=0.5, rely=0.9, relwidth=0.6, relheight=0.3, anchor="center")
# label3 = tk.Label(root, text="Label 3", bg="blue", fg="white")
# # Sử dụng các thuộc tính place
# label3.place(x=200, y=200, width=150, height=100, anchor="center")
# =============================================================================
#
# =============================================================================
# menu = tk.Menu(root)
# file_menu = tk.Menu(menu, tearoff=0)
# menu.add_cascade(label="File", menu=file_menu)
# file_menu.add_command(label="New", command=hello)
# file_menu.add_command(label="Open", command=hello)
# file_menu.add_separator()
# file_menu.add_command(label="Exit", command=quit_app)
# edit_menu = tk.Menu(menu, tearoff=0)
# menu.add_cascade(label="Edit", menu=edit_menu)
# edit_menu.add_command(label="Cut", command=hello)
# edit_menu.add_command(label="Copy", command=hello)
# edit_menu.add_command(label="Paste", command=hello)
# root.config(menu=menu)
# =============================================================================
#
# =============================================================================

# button = tk.Button(root, text="Chọn màu sắc", command=choose_color)
# button.pack()
# =============================================================================
#
# =============================================================================
# cal = DateEntry(
#     root,
#     width=12,
#     background="darkblue",
#     foreground="white",
#     borderwidth=2,
#     state="read",
# )
# cal.pack(padx=10, pady=10)
# button = tk.Button(root, text="Lấy ngày", command=get_date)
# button.pack(pady=5)
# =============================================================================
#
# =============================================================================
tree = ttk.Treeview(root)
tree["columns"] = ("Name", "Age")
tree.column("#0", width=30, stretch=tk.NO)
tree.column("Name", width=100, minwidth=100, stretch=tk.NO)
tree.column("Age", width=100, minwidth=100, stretch=tk.NO)
# Thêm tiêu đề cho các cột
tree.heading("#0", text="ID", anchor="center")
tree.heading("Name", text="Name", anchor="center")
tree.heading("Age", text="Age", anchor="center")
# Thêm dữ liệu vào TreeView
tree.insert("", "end", text="5", values=("John Doe", "30"))
tree.insert("", "end", text="2", values=("Jane Smith", "25"))
tree.insert("", "end", text="3", values=("Bob Johnson", "35"))
tree.pack()
# =============================================================================
#
# =============================================================================
# tree = ttk.Treeview(root)
# tree["columns"] = "Value"
# # Define columns
# tree.column("#0", width=150, minwidth=100, stretch=tk.NO)
# tree.column("Value", width=100, minwidth=100, stretch=tk.NO)
# # Headings
# tree.heading("#0", text="Parent", anchor=tk.W)
# tree.heading("Value", text="Value", anchor=tk.W)
# # Add data
# parent1 = tree.insert("", "end", text="Parent 1", values=("Value 1"))
# tree.insert(parent1, "end", text="Child 1", values=("ChildValue 1"))
# tree.insert(parent1, "end", text="Child 2", values=("ChildValue 2"))
# parent2 = tree.insert("", "end", text="Parent 2", values=("Value 2"))
# tree.insert(parent2, "end", text="Child 3", values=("ChildValue 3"))

# # Buttons
# add_button = tk.Button(root, text="Add Child", command=add_child)
# add_button.pack(pady=10)
# tree.pack()


root.mainloop()


def b1():

    def check_login():
        username = username_entry.get()
        password = password_entry.get()
        with open("list.json", "r") as file:
            users = json.load(file)
        if username in users and users[username] == password:
            messagebox.showinfo("Login", "Login successful!")
        else:
            messagebox.showerror("Login", "Invalid username or password")

    root = tk.Tk()
    root.attributes("-topmost", True)
    root.title("Login Form")
    username_label = tk.Label(root, text="Username:", bg="red", anchor="center")
    username_label.grid(row=0, column=0, padx=5, pady=5)
    username_entry = tk.Entry(root, width=30)
    username_entry.focus()
    username_entry.grid(row=0, column=1, padx=5, pady=5)

    password_label = tk.Label(root, text="Password:", bg="blue")
    password_label.grid(row=1, column=0, padx=5, pady=5, sticky="news")
    password_entry = tk.Entry(root, show="*", width=30)
    password_entry.grid(row=1, column=1, padx=5, pady=5)
    root.columnconfigure(0, weight=1)
    root.columnconfigure(1, weight=1)
    root.rowconfigure(0, weight=1)
    root.rowconfigure(1, weight=1)
    root.rowconfigure(2, weight=3)
    # Tạo nút đăng nhập
    login_button = tk.Button(
        root, text="Login", command=check_login, font=("Times 20 underline")
    )
    login_button.grid(row=2, column=0, columnspan=2, ipady=30, ipadx=30)

    root.mainloop()


# b1()


def b2():
    def add_employee():
        name = entry_name.get()
        if name:
            listbox_employees.insert(tk.END, name)
            entry_name.delete(0, tk.END)

    def delete_employee():
        selected_index = listbox_employees.curselection()
        if selected_index:
            listbox_employees.delete(selected_index)

    root = tk.Tk()
    root.title("Employee List App")
    root.geometry("600x400+765+-5")
    root.attributes("-topmost", True)
    listbox_employees = tk.Listbox(root, bg="white", selectmode=tk.EXTENDED)
    listbox_employees.pack(padx=10, pady=10, fill="both", expand=False)
    entry_name = tk.Entry(root, bg="gray")
    entry_name.pack(padx=10, pady=(0, 100), fill="x")
    entry_name.focus()

    button_add = tk.Button(root, text="Add Employee", command=add_employee, bg="red")
    button_add.pack(
        padx=100,
        pady=(20, 5),
        fill="x",
    )
    button_delete = tk.Button(
        root, bg="gray", text="Delete Selected", command=delete_employee
    )
    button_delete.pack(padx=10, pady=(0, 10), fill="both", side="bottom")
    root.mainloop()


# b2()


def b4():
    def update_calendar(year, month):
        for widget in calendar_frame.winfo_children():
            widget.destroy()
        month_calendar = calendar.monthcalendar(year, month)
        days_labels = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
        for i, day in enumerate(days_labels):
            label = ttk.Label(calendar_frame, text=day)
            label.grid(row=0, column=i, padx=5, pady=5)
        for week_num, week in enumerate(month_calendar, start=1):
            for day_num, day in enumerate(week):
                if day != 0:
                    label = ttk.Label(calendar_frame, text=str(day))
                    label.grid(row=week_num, column=day_num, padx=5, pady=5)

    root = tk.Tk()
    root.title("Basic Calendar App")
    calendar_frame = ttk.Frame(root)
    calendar_frame.pack(padx=10, pady=10)
    current_date = datetime.datetime.now()
    year_label = ttk.Label(root, text="Year:")
    year_label.pack(side="left", padx=5, pady=5)
    year_combo = ttk.Combobox(root, values=list(range(2000, 2101)), state="read")
    year_combo.pack(side=tk.LEFT, padx=5, pady=5)
    month_label = ttk.Label(root, text="Month:")
    month_label.pack(side=tk.LEFT, padx=5, pady=5)
    month_combo = ttk.Combobox(root, values=list(range(1, 13)), state="read")
    month_combo.pack(side=tk.LEFT, padx=5, pady=5)
    month_combo.set(current_date.month)
    update_button = ttk.Button(
        root,
        text="Update Calendar",
        command=lambda: update_calendar(int(year_combo.get()), int(month_combo.get())),
    )
    update_button.pack(side="bottom", padx=5, pady=5)
    update_calendar(current_date.year, current_date.month)
    root.mainloop()


# b4()


def b5():

    class SeatBookingSystem:
        def __init__(self, master, rows, cols):
            self.master = master
            self.rows = rows
            self.cols = cols
            self.seats = [
                [False for _ in range(cols)] for _ in range(rows)
            ]  # Matrix to track seat status

            self.create_seating_plan()

        def create_seating_plan(self):
            for i in range(self.rows):
                for j in range(self.cols):
                    button = tk.Button(
                        self.master,
                        text=f"Seat {i+1}-{j+1}",
                        width=10,
                        height=2,
                        command=lambda row=i, col=j: self.toggle_seat(row, col),
                    )
                    button.grid(row=i, column=j)

        def toggle_seat(self, row, col):
            if self.seats[row][col]:  # If seat is already booked
                self.seats[row][col] = False
                self.master.grid_slaves(row=row, column=col)[0].configure(bg="white")
                print(f"Seat {row+1}-{col+1} has been unbooked.")
            else:
                self.seats[row][col] = True
                self.master.grid_slaves(row=row, column=col)[0].configure(bg="green")
                print(f"Seat {row+1}-{col+1} has been booked.")

    def main():
        root = tk.Tk()
        root.title("Seat Booking System")

        rows = 5
        cols = 5

        app = SeatBookingSystem(root, rows, cols)

        root.mainloop()

    if __name__ == "__main__":
        main()


# b5()


def bt3():
    def send_message():
        message = entry_message.get()
        if message:
            text_history.config(state=tk.NORMAL)
            text_history.insert(tk.END, f"You: {message}\n")
            text_history.config(state=tk.DISABLED)
            entry_message.delete(0, tk.END)

    root = tk.Tk()
    root.title("Chat Application")
    root.geometry("800x600+100+50")
    root.attributes("-topmost", True)
    # Lịch sử tin nhắn
    text_history = tk.Text(root, height=20, width=50, state="disable", bg="gray")
    text_history.grid(row=0, column=0, padx=10, pady=10, sticky="news", columnspan=2)

    # Trường nhập tin nhắn
    entry_message = tk.Entry(root, width=40)
    entry_message.focus()
    entry_message.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="ew")

    # Nút gửi tin nhắn
    send_button = tk.Button(root, text="Send", command=send_message)
    send_button.grid(row=1, column=1, padx=10, pady=(0, 10), sticky="news")

    # Đảm bảo các phần tử có thể mở rộng theo kích thước của cửa sổ
    root.grid_rowconfigure(0, weight=10)
    root.grid_columnconfigure(0, weight=10)
    root.grid_rowconfigure(1, weight=1)
    root.grid_columnconfigure(1, weight=1)

    root.mainloop()


# bt3()
