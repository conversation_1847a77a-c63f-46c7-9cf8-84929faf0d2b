# from typing import Union, Any

# name: str = "<PERSON>"
# age: int = 29
# height_metres: Union[int, float] = 1.87
# names: list = ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
# names: list[str] = ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
# random_values: list[Union[str, int]] = ["x", 13, "camel", 0]
# movie: tuple[str, str, int] = ("Toy Story 3", "<PERSON> Unkrich", 2010)
# # ///////////////////////
# name: str = "<PERSON>"
# age: int = 29
# height_metres: Any = 1.87
# # ///////////////////////
# movies: list[tuple[str, str, int]] = [
#     ("Finding Nemo", "Andrew Stanton", 2005),
#     ("Inside Out", "Pete Docter", 2015),
#     ("Toy Story 3", "<PERSON> Un<PERSON>rich", 2010),
# ]
# Movie = tuple[str, str, int]
# movies: list[Movie] = [
#     ("Finding Nemo", "<PERSON> Stanton", 2005),
#     ("Inside Out", "Pete Docter", 2015),
#     ("Toy Story 3", "<PERSON> Unkrich", 2010),
# ]
# //////////////////////

from typing import Union

Movie = tuple[str, str, int]


def find_movie(search_term: str, movies: list[Movie]) -> Union[Movie, None]:
    # Optional[Movie]
    for title, director, year in movies:
        if title == search_term:
            return (title, director, year)
    return None


def show_movies(movies: list[Movie]):
    for movie in movies:
        print_movie(movie)


def print_movie(movie: Movie):
    title, director, year = movie
    print(f"{title} ({year}), by {director}")


movies: list[Movie] = [
    ("Finding Nemo", "Andrew Stanton", 2005),
    ("Inside Out", "Pete Docter", 2015),
    ("Toy Story 3", "Lee Unkrich", 2010),
]

show_movies(movies)

search_result: Union[Movie, None] = find_movie("asd Nemo", movies)

if search_result:
    print_movie(search_result)
else:
    print("Couldn't find movie.")
