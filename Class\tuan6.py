import random
import sys
import json
from datetime import datetime, timedelta


today = datetime.today()
ngayBD = today - timedelta(days=365 * random.randint(1, 3))  # Ngẫu nhiên 1-3 nam truoc
ngayKT = ngayBD + timedelta(days=random.randint(1, 365))  # Ngẫu nhiên 1-365 ngày
print(ngayBD, ngayKT, sep="\n")
date_string_BD = ngayBD.strftime("%d-%m-%Y")
date_string_KT = ngayKT.strftime("%d-%m-%Y")
print(date_string_BD, date_string_KT, sep="\n")


def info_python():
    version_info = sys.version_info
    print("Số phiên bản chính:", version_info.major)
    print("Số phiên bản nhỏ:", version_info.minor)
    print("Số phiên bản con:", version_info.micro)
    print("<PERSON><PERSON><PERSON> ph<PERSON><PERSON> hành:", version_info.releaselevel)
    print("Số serial:", version_info.serial)
    print(sys.argv[0])
    print(sys.executable)  # /home/<USER>/venv3/bin/python
    print(sys.platform)  # linux


# info_python()


def guess_number():
    secret_number = random.randint(1, 5)
    print(secret_number)
    while True:
        try:
            guess = int(input("Hãy đoán số từ 1 đến 5: "))
            if guess == 0:
                break
        except ValueError:
            print("Bạn cần nhập một số nguyên.")
            continue
        if guess < secret_number:
            print("Số của bạn quá thấp. đoán tiếp hoặc 0 để thoát")
        elif guess > secret_number:
            print("Số của bạn quá cao. đoán tiếp hoặc 0 để thoát")
        else:
            print("Chúc mừng! Bạn đã đoán đúng số.")
            break
    try:
        choice = input("Bạn có muốn tiếp tục chơi không?,(Nhập 0 để thoát): ")
        if choice.strip() == "0":
            sys.exit(999)
        else:
            guess_number()
    except KeyboardInterrupt:
        sys.exit(666)


# guess_number()


def math():
    def question():
        operators = ["+", "-", "*", "/"]
        operator = random.choice(operators)
        num1 = random.randint(1, 15)
        num2 = random.randint(1, 15)
        if operator == "/":
            # Đảm bảo phép chia có kết quả là số nguyên
            while num1 % num2 != 0:
                num1 = random.randint(1, 20)
                num2 = random.randint(1, 20)
        return num1, operator, num2  # tra ve phep toan va 2 so random

    def answer(question):
        num1, operator, num2 = question
        solution = input(f"{num1} {operator} {num2} = ")
        return solution  # Kqua user

    def check_solution(question, answer):
        num1, operator, num2 = question
        correct_solution = eval(f"{num1} {operator} {num2}")  # THien Bthuc
        return int(answer) == correct_solution  # bool

    def main():
        point = 0
        while True:
            problem = question()
            KQ = answer(problem)  # KQua user
            if check_solution(problem, KQ):
                print("Chính xác!")
                point += 1
                continue
            else:
                print(
                    "Sai! Ket qua:",
                    eval(f"{problem[0]} {problem[1]} {problem[2]}"),
                    f"diem: {point}",
                )
            if KQ.lower() == "exit":
                print("OUT")
                sys.exit(333)

            try:
                choice = input(
                    "Bạn muốn tiếp tục chơi không? (Nhấn Enter để tiếp tục, hoặc nhập '0' để kết thúc): "
                )
                if choice.strip().lower() == "0":
                    sys.exit(999)
                else:
                    point = 0
            except KeyboardInterrupt:
                sys.exit(666)

    main()


# math()
