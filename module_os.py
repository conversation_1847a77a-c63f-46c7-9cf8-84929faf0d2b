#  ================================================================Quản lý Tiến Trình (Process Management)=============================================================
# os._exit(n): Dừng tiến trình ngay lập tức . không dọn dẹp (không gọi finally, không xả buffer I/O). >< sys.exit(n):cleanup
# os.abort():  kết thúc ngay lập tức với một thông báo lỗi : gửi tín hiệu SIGABRT đến tiến trình
# os.getpid():Trả về process ID của tiến trình hiện tại.(84,11984,10492,456)
# os.getppid(): Tr<PERSON> về parent process ID của tiến trình hiện tại.
# ❓unix-like os.kill(pid,sig): G<PERSON><PERSON> tín hiệu sig đến tiến trình có process ID là pid (import signal ; SIGINT SIGTERM SIGKILL SIGHUP SIGQUIT SIGSTOP SIGCONT	SIGALRM)
# ❗❗ os.system(command) Thực thi một lệnh shell. Trả về mã trạng thái của lệnh 0 =succes. (os.system("cd D:\\stuff & dir") os.system("ping google.com"))
################################### P_WAITT:  cha chờ  con kết thúc rồi mới tiếp tục. => mã thoát của con = 0.
################################### P_NOWAIT:  cha không chờ  con, chạy song song với  con.=> PID của con.
################################### P_NOWAITO: = P_NOWAIT,dành cho các hệ thống không trả về mã thoát của  con. => PID của con/None
################################### P_DETACH :Tạo con độc lập hoàn toàn (detached), không liên kết với terminal/console. (use background)
################################### P_OVERLAY: Thay thế hiện tại bằng  mới= exec()
# os.spawnl(mode : P_NOWAIT, path, *args) : tạo một tiến trình con(find PATH): os.spawnl(os.P_NOWAIT, "C:\\Windows\\System32\\notepad.exe", "notepad.exe")
# os.spawnv(mode, path, args)  đối số cho chương trình mới được truyền dưới dạng một sequence (ví dụ: list hoặc tuple) args.
# os.execl(path, *args): Thực thi  chương trình mới , thay thế (overlay) hiện tại  os.execl(r"C:\Windows\System32\notepad.exe", "notepad.exe", "TB.txt")
# os.execlp(file, *args) = execl(), tìm kiếm file thực thi trong các thư mục os.environ["PATH"]
# os.execv(path, args):=execl(), đối số  sequence list/tuple)  command = ["C:\\Windows\\System32\\notepad.exe", "TB.txt"] os.execv(command[0], command)
# os.execvp(file, args) = os.execv(), nhưng tìm kiếm file thực thi trong PATH.  command = ["notepad.exe", "final_document.txt"] os.execvp(command[0], command)
# ❗❗ os.popen(command="dir/ipconfig", mode="r/w", bufsize=-1): Mở một pipe đến hoặc từ một lệnh. Trả về một đối tượng file  đọc hoặc ghi vào lệnh.


# ==============================================================Quản lý File Descriptors (File Descriptor Management):=============================================
# os.closerange(fd_low, fd_high):Đóng tất cả các file descriptors trong khoảng từ fd_low (bao gồm) đến fd_high (không bao gồm)
# os.dup(fd1):  Tạo ra một bản sao của file descriptor fd (gốc = 3, dup =4, dup =5).
# os.fdopen(fd, *args, **kwargs): Trả về một đối tượng file (file object) được kết nối với fd(os.open).cho phép read write
# os.open(path, flags, mode=0o777):Mở/tạo một file  với flags (ví dụ: O_APPEND/BIN/CREATE/EXCL/TEXT/RANDOM/O_SHORT_LIVED/O_TEMPORARY) và -> fd (int).
#################################### O_ACCMODE để lấy ra các bit xác định chế độ truy cập (read/write) của file.
#################################### O_APPEND    (append mode).
#################################### O_BINARY chế độ nhị phân (binary mode) (Windows).
#################################### O_CREAT  Tạo file mới nếu file chưa tồn tại.báo lỗi nếu file đã tồn tại (không ghi đè).
#################################### O_EXCL Kết hợp với    O_CREAT để báo lỗi nếu file đã tồn tại (không ghi đè).
#################################### O_RANDOM  Tối ưu hóa cho truy cập file ngẫu nhiên (Windows).
#################################### O_RDONLY   (read only).
#################################### O_RDWR   (read & write).
#################################### O_SEQUENTIAL Tối ưu hóa cho truy cập tuần tự (sequential) (Windows).
#################################### O_SHORT_LIVEDFile   tồn tại trong thời gian ngắn (Windows).
#################################### O_TEMPORARYFile tạm thời, sẽ bị xóa khi đóng
#################################### O_TEXT văn bản (text mode) (Windows)
#################################### O_TRUNC  Nếu file đã tồn tại, (xóa hết dữ liệu cũ)
#################################### O_WRONLY   (write only).
# os.fstat(fd):  thông tin trạng thái của file được tham chiếu bởi  fd. kích thước, thời gian sửa đổi, quyền, v.v.
# os.fsync(fd):   ghi tất cả các thay đổi của fd xuống đĩa.
# os.ftruncate(fd, length):Thay đổi kích thước của file được tham chiếu bởi file descriptor fd thành length bytes. (chế độ rb+) os.ftruncate(fd, 10)
#  os.truncate(path, length)  Thay đổi kích thước của file tại đường dẫn path thành length bytes. os.truncate("sample.txt", 5)
# os.read(fd, n): Đọc tối đa n bytes từ fd.
# os.pipe()->r,w : Tạo một pipe (một chiều,  1 luồng->1 luồng ) và trả về một cặp file descriptors (r, w) cho đầu đọc và đầu ghi (đọc 1 lần=hết)
# os.get_inheritable(fd): file descriptor fd có thể được kế thừa bởi các tiến trình con hay không
# os.isatty(fd): kiểm tra xem (FD) có phải là một terminal hay không (sys.stdout.fileno() , file.fileno())


# ====================================================== Quản lý Đường Dẫn và Thư Mục (Path and Directory Management):================================================
# os.access(path, mode): check quyền truy cập vào đường dẫn path theo chế độ mode (EX/F/R/W/X_OK )
# os.chdir(path): chuyển đổi thư mục hien tai sang path os.chdir("D:\\C#|pygame")
# os.getcwd(): Lấy thư mục hien tai ||  os.getcwdb(): Lấy thư mục hien tai (bytes)
# os.chmod(path, mode): thay đổi quyền của path (vd: 0o765) os.chmod("chmod_test.txt", 0o444)
# os.listdir(path="." "C:\\" ): Trả về một danh sách chứa tên của các entry (file và thư mục)
# os.scandir(path='.| C://'): => iterator os.DirEntry = entry trong path. Hiệu quả > os.listdir()  (stat, inode,name,path).
# os.walk(top, topdown=True, onerror=None, followlinks=False): cây thư mục  duyệt đệ quy qua các thư mục bên dưới top.=> root,dirs,files
# ❗❗ os.listdrives: Hàm này trả về một list các chuỗi,['C:\\', 'D:\\', 'E:\\', 'F:\\', 'G:\\']. *drives = os.listdrives()
########################## Direntry (scandir ): entry.__fspath__() : đường dẫn dưới dạng chuỗi
# os.fspath(path):Trả về biểu diễn đường dẫn của hệ thống tệp của đối tượng đường dẫn. agr: str | bytes | path(pathlib).(dùng để đảm bảo là path)
# os.lstat(path/ "example.txt" ): trả về thông tin về file hoặc thư mục tại đường dẫn path (quyền,inode,hardlink,uid,gid,size,access/modify/create time )
#  os.stat(path) Trả về thông tin trạng thái của một file hoặc thư mục || (oct(st_mode))
# os.makedirs(path, exist_ok=True):Tạo thư mục và các thư mục con nếu chúng chưa tồn tại. (đệ quy)
# os.removedirs(path):Xóa thư mục và các thư mục cha nếu chúng trống. Nếu thư mục không trống, nó sẽ gây lỗi. "G:/path/to/new/directory" dir-new-to-path (đệ quy)
# os.mkdir(path, mode=0o777): Tạo một thư mục có tên path với mode số được chỉ định.(tạo 1, ko đệ quy)
# os.rmdir(path): Xóa một thư mục rỗng có tên path.  OSError (xóa 1, ko đệ quy)
# os.link(src, dst): Tạo một hard link trỏ đến file src, đồng bộ
# os.symlink(src, dst) Tạo một liên kết tượng trưng trỏ đến src có tên là dst (chủ yếu trên các hệ thống giống Unix).
# os.remove(path): Xóa một file hoặc một liên kết tượng trưng tại đường dẫn path
# os.unlink(path) Xóa một file hoặc một liên kết tượng trưng tại đường dẫn path (tương tự như os.remove()).
# os.rename(src, dst): Đổi tên file hoặc thư mục từ đường dẫn src thành dst. (lỗi exists, kh ghi đè)
# os.renames(old, new) Đổi tên file/folder . có thể tạo  fodler trung gian. xóa folder cũ nếu trống  "G://path/to/file1.txt" -> "G://new/location/file1.txt"
# os.replace(old/src, new/dst):  di chuyển file/thư mục,( src mất dst mới/ghi đè, không lỗi exists).
# ❗WINDOW❗ os.startfile(path[, operation]) : 2click . operation :'open', 'print','edit', 'explore','find'). show_cmd=0/1/3/6
########################################### os.startfile("https://chat.openai.com")
########################################### os.startfile("notepad.exe")
########################################### os.startfile("cmd.exe")  # Mở cửa sổ CMD riêng
########################################### os.startfile("hello.exe", cwd="D:\\Code\\C", show_cmd=6)
########################################### os.startfile("open.exe", cwd="D:\\Code\\C", show_cmd=0) # có thực hiện, không show cmd
########################################### os.startfile("cmd.exe", arguments="/k ping google.com & pause", show_cmd=1)
########################################### os.startfile("cmd.exe", arguments="/k D: & dir & pause", show_cmd=1)
########################################### os.startfile("C:\\Users\\<USER>\\Documents")  # 'explore' def, 'find':focus search box
# ❗❗ os.utime(path, times=None, *, ns=None, follow_symlinks=True) Thay đổi truy cập và sửa đổi . os.utime(file_to_utime, (access_time, modify_time))


# ================================================================== Môi Trường (Environment):=====================================================================
# os.get_exec_path(env=None|dict:k:vl;vl)  :  danh sách các thư mục biến môi trường PATH (environment...)
# os.getenv(key, default=None): Trả về giá trị của biến môi trường key. Nếu biến không tồn tại, trả về default hoặc None. (Path,PSModulePath<Temp,Local,OneDrive)
# ❌os.putenv(key, value) : Đặt giá trị của biến môi trường key thành value. ảnh hưởng  tiến trình con ||  os.environ hiệu quả hơn
# os.environ.items() : trả về một danh sách các cặp key-value của biến môi trường hiện tại || os.environ["hehe"] = "haha" del os.environ["hehe"]
# ❌os.unsetenv(key) Xóa biến môi trường key. Thay đổi này ảnh hưởng đến các tiến trình con được tạo sau này


# ============================================================================= Thông Tin Hệ Thống (System Information):============================================
# os.cpu_count():Trả về số lượng CPU (lõi) có trong hệ thống.
# os.device_encoding(fd):Chuỗi tên encoding mà thiết bị đầu ra vào dùng (vd: 'cp437', 'utf-8', 'ANSI_X3.4-1968')
# os.get_terminal_size()Trả về kích thước của terminal lket vs  fd dưới dạng một đối tượng os.terminal_size chứa số cột và số dòng(columns , lines)
# os.getlogin() : Trả về tên của người dùng đã đăng nhập vào terminal.(khanh/pc)
# os.times()  (user time, system time, children's user time, children's system time, elapsed real time).
# os.name: tên  module os phụ thuộc vào hệ điều hành đang chạy. 'nt'  Windows, 'posix'  Unix-like , 'java'  Java virtual


# ==========================================================================================Khác (Miscellaneous:===================================================
# ❌os.fsencode(filename): Mã hóa tên file (str sang bytes).
# ❌os.fsdecode(filename):  Giải mã một tên file từ kiểu byte sang kiểu chuỗi, sử dụng encoding của hệ thống tệp.
# os.urandom(n) Trả về một chuỗi n byte ngẫu nhiên phù hợp cho mục đích mật mã
# ❗ _getvolumepathname(path): volume (ổ đĩa) . C:\Users\<USER>\


# ================================================================Thao tác với Đường Dẫn==========================================================
# os.path.abspath(path):đường dẫn tuyệt đối (tương -> tuyệt  / -> \)
# os.path.join(path, *paths): Kết hợp một hoặc nhiều phần (\ : Windows) chỉ lấy đườngtuyệt đối trở đi ("C:\\Users", "Public", "Documents") ("fol1", "fol2", "file.txt")
# os.path.split(path): => (dirname, basename).Nếu kết thúc bằng dấu phân tách, basename sẽ là một chuỗi rỗng. ('C:\\Users\\<USER>\\Documents', 'report.txt')
# os.path.splitdrive(path):  cặp (drive, tail),  ổ đĩa hoặc tiền tố UNC ( 'C:\\', '\\\\server\\share') . ('C:\\', 'Users\\Public\\Documents\\report.txt')
# os.path.splitext(path): cặp (root, ext) Nếu không có phần mở rộng, ext chuỗi rỗng. os.path.splitext("document.pdf.exe") => ("document.pdf", ".exe")
# os.path.splitroot(path): Chia đường dẫn path thành 3 phần: (drive, root=\, tail) . ('C:', '\', 'Users\Public\Documents\report.txt')
# os.path.dirname(path): Tên thư mục  của path..không chứa dấu phân tách => về một chuỗi rỗng vd:  'C:\\Users\\<USER>\\Documents'
# os.path.basename(path): tên file của  path. kết thúc bằng dấu phân tách \\ => chuỗi rỗng.  'report.txt'
# os.path.normcase(path): Chuẩn hóa case .  chữ thường và (/) => (\) "C:\\Users\\<USER>\\Documents\\REPORT.TXT" => 'C:\\Users\\<USER>\\Documents\\report.txt'
# os.path.normpath(path): Chuẩn hóa áp dụng .|.. /=>\ không check exists|| ("C:\\Users\\<USER>\\Public\\..\\Documents\\file.txt") => 'C:\Users\<USER>\file.txt'
# os.path.realpath(path) trả về đường dẫn tuyệt đối thực tế của path, đã được giải quyết toàn bộ symbolic link (liên kết tượng trưng) nếu có.
# os.path.relpath(path,start):  trả về đường dẫn tương đối từ thư mục start đến path.  "C:\\Users\\<USER>\\report.txt" ,"C:\\Users" => 'Public\report.txt'


# ================================================================Kiểm tra Thuộc Tính Đường Dẫn==========================================================
# os.path.exists(path)  path tồn tại , và False nếu không.
# os.path.isabs(path):check đường dẫn tuyệt đối (bắt đầu bằng tên ổ đĩa hoặc tiền tố UNC trên Windows) (C:\ ; \\folder1\b)
# os.path.isdir(path): check thư mục , False  là file, symbolic link (nếu có), hoặc không tồn tại.
# os.path.isfile(path): check file , False  là thư mục, symbolic link (nếu có), hoặc không tồn tại.
# os.path.ismount(path):check điểm mount (mount point) . hệ thống tệp được gắn vào một hệ thống tệp khác . ["C:\\", "D:\\", "\\\\server\\share"]


# ================================================================Lấy Thông Tin Thời Gian==========================================================
# os.path.getatime(path): Thời gian truy cập cuối cùng của file hoặc thư mục dùng ctime
# os.path.getctime(path): Thời gian tạo của file hoặc thư mục.
# os.path.getmtime(path): Thời gian sửa đổi cuối cùng của file hoặc thư mục.
# os.path.getsize(path): Kích thước của file hoặc thư mục.

import os.path

# ================================================================So sánh==========================================================
# os.path.samefile(path1, path2):True:cả hai tham chiếu cùng một file vật lý/thư mục. so sánh số inode os.symlink(file1, symlink) os.path.samefile(file1, symlink
# os.path.sameopenfile(fp1, fp2): True:  các đối tượng file mở fp1 và fp2 tham chiếu đến cùng một file vật lý  with open(file1, "r") as fp1, open(file1, "r") as fp2:
# os.path.samestat(stat1, stat2): True nếu các đối tượng stat trả về bởi os.f/l/stat() tham chiếu cùng một file. so sánh  số inode và device number.


# ================================================================Khác==========================================================
# os.path.commonpath(paths): Trả về đường dẫn chung dài nhất  các đường dẫn trong . ValueError: paths rỗng hoặc ổ đĩa khác nhau. ["C:\\Users\\<USER>\\Users\\GiaSu2.e"]
# os.path.❌commonprefix(list): Trả về tiền tố đường dẫn chung dài nhất   ["flower.jpg", "flow.png", "flight.bmp"] => "fl"
# os.path.expanduser(path): đường dẫn mới tham chiếu thư mục người dùng ( ~ / ~username)| ~\\Documents => C:\Users\<USER>\Documents  ~Public\\Desktop=> C:\Users\<USER>\Desktop
