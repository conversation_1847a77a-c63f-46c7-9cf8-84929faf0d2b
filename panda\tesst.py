from matplotlib import pyplot as plt
import pandas as pd

bestsellers = pd.read_csv("bestsellers.csv")
titanic = pd.read_csv("titanic.csv")
countries = pd.read_csv("world-happiness-report-2021.csv")
movies = pd.read_csv("netflix_titles.csv")
houses = pd.read_csv("kc_house_data.csv")
pokemons = pd.read_csv("Pokemon.csv")
# ============================================================
# print(titanic.age.value_counts(dropna=False))
# titanic.age = titanic.age.astype(float)
# print(titanic.age.mean())
# titanic.sex = titanic.sex.astype("category")
# print(titanic.info())
# print(pd.to_numeric(titanic["age"]).value_counts(dropna=False ))
titanic.fill()