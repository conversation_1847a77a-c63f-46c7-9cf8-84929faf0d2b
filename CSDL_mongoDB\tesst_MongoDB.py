from pprint import pprint
import pymongo

try:

    client = pymongo.MongoClient(
        "mongodb+srv://khanh5211:<EMAIL>/"
    )

    # print(client)
    mydb = client["test"]
    my_collection = mydb["SINHVIEN"]
    # print(my_collection.find_one())
    ######################################################################
    # myquery = {"age": 20}
    # myquery = {"age": {"$gt": 26}}
    # myquery = {"age": {"$lt": 26}}
    # myquery = {"name": {"$regex": "^k"}}
    ###################################################################
    # myquery = {"name": {"$regex": "^t"}}
    # x = my_collection.delete_many(myquery)
    # print(x.deleted_count)
    ###################################################################
    # myquery = {"age": {"$gt": 30}}
    # new_vl = {"$set": {"gender": "female"}}
    # x = my_collection.update_many(myquery, new_vl)
    # print(x.modified_count)
    ###################################################################
    # insert_vl = [
    #     {"name": "trang", "age": 27, "gender": "female"},
    #     {"name": "tran", "age": 17, "gender": "female"},
    #     {"name": "ung", "age": 37, "gender": "male"},
    #     {"name": "luc", "age": 23, "gender": "male"},
    #     {"name": "dieu", "age": 21, "gender": "female"},
    #     {"name": "hien", "age": 24, "gender": "female"},
    #     {"name": "thy", "age": 40, "gender": "female"},
    #     {"name": "tien", "age": 35, "gender": "male"},
    # ]
    # x = my_collection.insert_many(insert_vl)
    # print(x.inserted_ids)
    ###################################################################
    # myquery = {"age": {"$gt": 30}}
    for i in my_collection.find().limit(5).sort("age", -1):
        print(i)
    # docs = list([i for i in my_collection.find()])
    # pprint(docs)
    ######################################################################
    #  create a new database connection
    new_con = mydb["GIAOVIEN"]
    # insert_vl = [
    #     {"name": "Atrox", "age": 27, "subject": "math"},
    #     {"name": "Blitrank", "age": 17, "subject": "history"},
    #     {"name": "Camile", "age": 37, "subject": "physics"},
    # ]
    # x = new_con.insert_many(insert_vl)
    # print(x.inserted_ids)
    ######################################################################
    new_con.drop()
    ######################################################################
    # print(mydb.list_collection_names())
except:
    print("Something went wrong")
