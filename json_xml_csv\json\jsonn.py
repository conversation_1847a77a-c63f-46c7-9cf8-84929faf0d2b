import json


def test1():
    data = {"name": "<PERSON>", "age": 30}
    with open("data.json", "w") as file:
        json.dump(data, file)

    json_str = '{"ho va ten": "<PERSON>", "age": 21}'
    data = json.loads(json_str)  # str -> dict or list[dict]
    print(data)

    # Output: {'name': '<PERSON>', 'age': 30}
    with open("data.json", "r") as file:
        data = json.load(file)  # ghi file  list[dict] or dict
    print(data)

    data = {"Name": "<PERSON>", "age": 18, "class": "12A8"}
    json_str = json.dumps(data, indent=4)  # list[dict] or dict -> str
    print(json_str)
    # Output: '{"name": "<PERSON>", "age": 30}'


def test2():
    data = [
        {
            "name": "a",
            "price": 40000,
            "ingredients": ["a1", "a2", "a3", "a4", "a5"],
            "vegetarian": False,
        },
        {
            "name": "b",
            "price": 99000,
            "ingredients": ["b1", "b2", "b3", "b4", "b5"],
            "vegetarian": False,
        },
    ]
    json_string = json.dumps(data, ensure_ascii=False, indent=4)  # dùng để write
    with open("menu_item.json", "w", encoding="utf-8") as file:
        json.dump(data, file, ensure_ascii=False, indent=4)
        # file.write(json_string)

    with open("menu.json", "r", encoding="utf-8") as file:
        data1 = json.load(file)

    # Phân tích dữ liệu JSON
    for item in data1["menu"]:
        print(f"Name: {item['name']}")
        print(f"Price: {item['price']}")
        print(f"Ingredients: {', '.join(item['ingredients'])}")
        print(f"Vegetarian: {'Yes' if item['vegetarian'] else 'No'}")
        print()  # Dòng trống giữa các mục
