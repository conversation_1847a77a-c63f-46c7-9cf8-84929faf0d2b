import requests
import json


cookies = {
    "ASP.NET_SessionId": "onvgvrnd31oyawh0lpidu2fs",
    "favorite_stocks_state": "1",
    "favorite_stocks_state": "1",
    "__RC": "5",
}

headers = {
    "Accept": "*/*",
    "Accept-Language": "vi,fr-FR;q=0.9,fr;q=0.8,en-US;q=0.7,en;q=0.6",
    "Connection": "keep-alive",
    "DNT": "1",
    "Referer": "https://cafef.vn/du-lieu/lich-su-giao-dich-symbol-vnindex/trang-1-0-tab-1.chn",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
    "sec-ch-ua": '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-gpc": "1",
    # 'Cookie': 'ASP.NET_SessionId=onvgvrnd31oyawh0lpidu2fs; favorite_stocks_state=1; favorite_stocks_state=1; __RC=5',
}

response = requests.get(
    "https://cafef.vn/du-lieu/Ajax/PageNew/DataHistory/PriceHistory.ashx?Symbol=VNINDEX&StartDate=01/02/2025&EndDate=02/22/2025&PageIndex=1&PageSize=20",
    cookies=cookies,
    headers=headers,
)
rs = response.json()
print(json.dumps(rs, indent=4))
