import time
from selenium import webdriver
from selenium.webdriver.common.by import By
import pandas as pd

browser = webdriver.Chrome()
browser.maximize_window()
browser.get("https://books.toscrape.com/index.html")
# tittle_tag = browser.find_element(
#     By.XPATH, "/html/body/div[1]/div/div/div/section/div[2]/ol/li[1]/article/h3/a"
# )
# print(tittle_tag.get_attribute("title"))

tittle_tag = browser.find_elements(
    By.XPATH, "/html/body/div[1]/div/div/div/section/div[2]/ol/li/article/h3/a"
)
title = [tag.get_attribute("title") for tag in tittle_tag]
print(title)
price_tag = browser.find_elements(
    By.XPATH, "/html/body/div[1]/div/div/div/section/div[2]/ol/li/article/div[2]/p[1]"
)
prices = [tag.text for tag in price_tag]
print(prices)
star_tag = browser.find_elements(
    By.XPATH, "/html/body/div[1]/div/div/div/section/div[2]/ol/li/article/p"
)
starts = [
    [x for x in star.get_attribute("class").split() if x != "star-rating"][0]
    for star in star_tag
]
print(starts)

df = pd.DataFrame({"title": title, "price": prices, "star": starts})
print(df)
time.sleep(1)
browser.quit()

# /html/body/div[1]/div/div/div/section/div[2]/ol/li[1]/article/h3/a
# /html/body/div[1]/div/div/div/section/div[2]/ol/li[2]/article/h3/a
# /html/body/div[1]/div/div/div/section/div[2]/ol/li[8]/article/h3/a

# /html/body/div[1]/div/div/div/section/div[2]/ol/li[13]/article/div[2]/p[1]
# /html/body/div[1]/div/div/div/section/div[2]/ol/li[20]/article/div[2]/p[1]

# /html/body/div[1]/div/div/div/section/div[2]/ol/li[20]/article/p
# /html/body/div[1]/div/div/div/section/div[2]/ol/li[16]/article/p
