from abc import ABC, abstractmethod
from datetime import datetime


class Notifier(ABC):
    @abstractmethod
    def send(self, message: str):
        pass


class EmailNotifier(Notifier):
    def send(self, message: str):
        print(f"Gửi email: {message}")


class SMSNotifier(Notifier):
    def send(self, message: str):
        print(f"Gửi SMS: {message}")


class SlackNotifier(Notifier):
    def send(self, message: str):
        print(f"Gửi tin nhắn Slack: {message}")


def get_notifier(channel: str) -> Notifier:
    if channel == "email":
        return EmailNotifier()
    elif channel == "sms":
        return SMSNotifier()
    elif channel == "slack":
        return SlackNotifier()
    else:
        raise ValueError("Kênh không xác định.")


class NotificationStrategy(ABC):
    @abstractmethod
    def should_notify(self, user_settings: dict) -> bool:
        pass


class NotifyAlways(NotificationStrategy):
    def should_notify(self, user_settings):
        return True


class NotifyBusinessHours(NotificationStrategy):
    def should_notify(self, user_settings):
        # <PERSON><PERSON><PERSON> gi<PERSON> hiện tại (ví dụ, 9h-17h)
        hour = datetime.now().hour
        return 9 <= hour < 2


class NotificationService:
    def __init__(self, notifier: Notifier, strategy: NotificationStrategy):
        self.notifier = notifier
        self.strategy = strategy

    def notify(self, user_settings, message):
        if self.strategy.should_notify(user_settings):
            self.notifier.send(message)


# Gửi thông báo qua email trong giờ hành chính
notifier_email = get_notifier("email")
strategy_business = NotifyBusinessHours()
service_email_business = NotificationService(notifier_email, strategy_business)
service_email_business.notify({}, "Hệ thống sẽ bảo trì vào tuần tới.")
# Output: (Nếu trong giờ hành chính) Gửi email: Hệ thống sẽ bảo trì vào tuần tới.

# Gửi thông báo SMS luôn
notifier_sms = get_notifier("sms")
strategy_always = NotifyAlways()
service_sms_always = NotificationService(notifier_sms, strategy_always)
service_sms_always.notify({}, "Khuyến mãi flash sale mới đã có!")
# Output: Gửi SMS: Khuyến mãi flash sale mới đã có!
