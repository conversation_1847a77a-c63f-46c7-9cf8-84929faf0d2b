import datetime
import math
import os as OS
import random
import re
import sys
import time

print("{0!r}".format("Đừng so sánh mình với bất cứ ai trong thế giới này."))  # repr
print(repr("Nếu bạn làm như vậy có nghĩa bạn đang sỉ nhục chính bản thân mình"))
print("{:%d %B %Y \t%H:%M:%S}".format(datetime.datetime.now()))
# decimal < digit < numeric


def stringg():
    """{0:18.9f}  so can ben phai, chuoi can ben trai / {0:b} / lap day` = 0: {:05d}  / {:^10.3f} ^:can giua~/</>/=:dấu bên trái,
    số bên phải /  {:*^5}: can giua~+lap day bang dau * == center /"""
    t1 = "Python Is Awesome Awesome Awesome"  # center string with char(optional) == lenght
    str = "xyz\t12345\tabc"
    t2 = t1.center(20, "♥")  # nếu lẻ trái < phải
    # center / ljust / rjust
    print(t1.casefold())  # casefold > lower : groß=> gross
    print(t1.endswith(("some", "any", "every")))  # anyy => true
    result = str.expandtabs()  # 8 16 24 32 40
    # default: moi đoạn mặc định là 8 pixxel :(xyz/t=5)(12345/t=3)
    print(result, len(result))
    print("is: {0:18.2f} va {1}".format(-2.3354, "asd"))
    print(str.lstrip("abc d"))  # xoa cac ky tu : a b c d "tab"
    print(str.partition("2"))  # => tuple : truoc, ky tu, sau // rpartition
    grocery = """Milk
    Chicken
    Bread
    Butter"""
    print(grocery.splitlines(True))  # giữ dấu / false: xóa dáu ngắt dòng
    print("-290".zfill(8))  # rjust, fill with 0
    grocery.min


# stringg()


def translate_maketrans():
    dict = {"a": "123", "b": "456", "c": "789"}
    string = "abcdef"
    translation = str.maketrans("abc", "ghi", "cde")  # translate table
    print(string.maketrans(translation))
    print(string.translate(translation))
    # example dictionary
    dict = {97: "123", 98: "456", 99: "789"}  # translate table
    string = "abc"
    print(string.translate(dict))

    # Tạo bảng dịch
    translation_table = str.maketrans("abc", "123", "def")
    # Dịch chuỗi sử dụng bảng dịch
    result = "abcxyz".translate(translation_table)
    print(result)  # Output: 123xyz
    # Thêm ví dụ với ký tự cần loại bỏ
    translation_table = str.maketrans("abc", "123", "xyz")  # xyz -> None
    result = "abcxyz".translate(translation_table)
    print(result)  # Output: 123


def IO():
    a, b, c = map(int, input("nhap a b c:").split())
    a, b = map(eval, input().split())
    ds = [int(i) for i in input().split()]
    a, b, c = input().split()
    ds = []
    for i in range(5):
        ds.append(int(input()))
    nhietdo = list(map(int, input("nhap day~: ").strip().split()))


def method_co_san():
    class gfg:
        vowels = ["a", "e", "i", "o", "u"]

        def __reversed__(self):
            return reversed(self.vowels)

    obj = gfg()
    print(list(reversed(obj)))

    class Vector:
        def __init__(self, x, y):
            self.x = x
            self.y = y

        def __add__(self, other):
            return Vector(self.x + other.x, self.y + other.y)

        def __sub__(self, other):
            return Vector(self.x - other.x, self.y - other.y)

        def __mul__(self, num):
            return Vector(self.x * num, self.y * num)

        def __str__(self):  # dung khi print
            return f"(toa do x:  {self.x},toa do y:  {self.y})"

    v1 = Vector(2, 3)
    v2 = Vector(5, 7)
    print(v1 + v2)  # Output: (7, 10)
    print(v1 - v2)
    print(v2 * 4)

    class Number:
        def __init__(self, value):
            self.value = value

        def __mod__(self, other):
            return self.value % other.value

        def __truediv__(self, other):
            return self.value / other.value

        def __lt__(self, other):  # less than
            return self.value < other.value

        def __le__(self, other):  # less equal
            return self.value <= other.value

        def __eq__(self, other):  # equal
            return self.value == other.value

        def __ne__(self, other):  # not equal
            return self.value != other.value

        def __gt__(self, other):
            return self.value > other.value

        def __ge__(self, other):
            return self.value >= other.value

    n1 = Number(10)
    n2 = Number(3)
    print(n1 % n2)
    print(n1 == n2)


def attrb_an():
    # __ attrb ẩn : không truy cập được từ bên ngoài
    class justcount:
        __count = 0

        def count(self):
            self.__count += 1
            print(self.__count)

    dem = justcount()
    dem.count()
    dem.count()
    dem.__count += 1  # error


def Arr2D_1D():
    # 2D -> 1D
    s = [[4, 8, 2], [4, 5, 7], [6, 1, 6]]
    ss = sum(s, [])
    print(ss)


def eval_input():
    [x * 5 for x in range(2, 10, 2)]
    str = input("nhap: ")
    print("out: ", eval(str))
    print(eval('"hello".upper()'))
    print(
        eval("5 == 5"),
        eval("4 < 10"),
        eval("8 + 4 - 2 * 3"),
        eval("'py ' * 5"),
        eval("10 ** 2"),
        eval("'hello' + 'py'"),
        eval("abs(-11)"),
    )


def doc_ghi_file():  # thắng
    foo = open("Foo.txt", "w")
    print("name file: ", foo.name)
    print("file close or not ? ", foo.closed)
    print("file mode:", foo.mode)
    foo.write("yeah sure about that\nyeh ey yo wtf guys!!")
    foo.close()

    foo_read = open("Foo.txt", "r+")
    str1 = foo_read.read(10)
    print(str1)
    pos = foo_read.tell()  # hien thi con tro dang tro
    print(pos)

    pos = foo_read.seek(0)  # tro ve vi tri
    str1 = foo_read.read(5)
    print(str1)
    pos = foo_read.tell()  # hien thi con tro dang tro
    print(pos)


def array2D():
    b = [[0] * 5 for i in range(5)]
    c = [[0 for j in range(5)] for i in range(5)]
    cuu_chuong = [[(i + 1) * (j + 1) for j in range(10)] for i in range(9)]


def fibonacci(n):
    if n == 0:
        return 1
    elif n == 1:
        return 1
    else:
        a, b = 1, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b
    if n == 1 or n == 2:
        return 1
    else:
        return fibonacci(n - 1) + fibonacci(n - 2)


def read_write_file():
    f1 = open("SGK.txt", "r+")
    data = f1.read(18)
    print(data)
    print("hehehe", file=f1)
    f1.write("hehe")
    f1.close()


def os():
    OS.makedirs("test")  # makedirs > mkdir
    OS.rmdir("test")  # removedirs >rmdir

    folder_path = r"D:\Code\C"
    chill = OS.listdir(folder_path)
    print(chill)

    print("Thư mục hiện hành:", OS.getcwd())
    print("Các mục trong thư mục hiện hành:", OS.listdir(r"D:\Code\C"))
    OS.mkdir("NewFolder")
    OS.rmdir("NewFolder")
    print(OS.path.exists("test.py"))
    print(OS.path.isfile("example.txt"))  # Output: True hoặc False
    print(OS.path.isdir("NewFolder"))  # Output: True hoặc False

    OS.chmod("SGK.txt", 0o777)
    # 6:doc va ghi 4: doc 2:ghi // abc:chu so huu/nhom/nguoi khac
    OS.makedirs(r"D:\test\python\test1")
    OS.makedirs(r"D:\test\python\test2")


def os_path():
    print(OS.path.dirname(r"D:\\Code\\python\\test.py"))
    path_tuple = OS.path.split(r"D:\Code\python\test1.py")
    print(path_tuple)
    size = OS.path.getsize("D:\\Code\\python\\random.docx")
    print(size)
    # Output: (kích thước của file tính bằng byte)
    nor = OS.path.normpath("D:\\Code\\python\\.\\test4.py")  # chuan hoa duong` dan~`
    print(nor)  # Output: D:\Code\python\example.py

    atime = OS.path.getatime("D:\\Code\\python\\test.py")
    print("Thời gian truy cập cuối cùng:", time.ctime(atime))
    print("Thời gian truy cập cuối cùng:", datetime.datetime.fromtimestamp(atime))


def bin_keyword():
    class Quantity:
        apple = 1
        orange = 2
        grapes = 2

        def __index__(self):  # chuyển đối tượng thành số
            return self.apple + self.orange + self.grapes

    a = Quantity()
    print("The binary equivalent of quantity is:", (a))


def callable_keyword():
    class Foo:
        def printLine(self):
            print("Print Something")

    class Foo:
        def __call__(self):  # cho phép đói tượng gọi như 1 hàm
            print("Print Something")

    print(callable(Foo))
    a = Foo()
    print(a())


def compile_keyword():
    codeInString = 'a = 8 ;b=7;sum=a+b;print("sum =",sum)'
    codeObject = compile(codeInString, "", "exec", optimize=2)  # source , filename,mode
    exec(codeObject)

    code_exec = """
    a = 10
    b = 20
    result = a + b
    print("Result (exec):", result)
    """
    compiled_exec = compile(code_exec, "", "exec")
    exec(compiled_exec)

    code_eval = "10 + 20"
    compiled_eval = compile(code_eval, "", "eval")
    result_eval = eval(compiled_eval)
    print("Result (eval):", result_eval)

    code_single = 'print("Result (single):", 10 + 20)'
    compiled_single = compile(code_single, "", "single")
    exec(compiled_single)

    # optimize=0: Không thực hiện tối ưu hóa.

    # optimize=1: Thực hiện tối ưu hóa cơ bản (loại bỏ các assert statement).

    # optimize=2: Thực hiện tối ưu hóa mạnh hơn (loại bỏ các assert statement và các câu lệnh __debug__).

    code_exec = """
    assert 1 == 2, "Assertion failed"
    print("Optimized code")
    """

    compiled_exec_default = compile(code_exec, "", "exec", optimize=-1)  # ~0
    compiled_exec_opt2 = compile(code_exec, "", "exec", optimize=2)  # ~1

    print("Running with default optimization:")
    try:
        exec(compiled_exec_default)
    except AssertionError as e:
        print(e)

    print("Running with optimize=2:")
    try:
        exec(compiled_exec_opt2)
    except AssertionError as e:
        print(e)


def classmethod_keyword():
    class Student:
        marks = 0

        def compute_marks(self, obtained_marks):
            marks = obtained_marks
            print("Obtained Marks:", marks)

    Student.print_marks = classmethod(Student.compute_marks)
    Student.print_marks(88)

    #####################################
    class Person:
        age = 25

        def printAge(cls):
            print("The age is:", cls.age)

    Person.printAge = classmethod(Person.printAge)
    Person.printAge()
    ##############################
    from datetime import date

    class Person:
        def __init__(self, name, age):
            self.name = name
            self.age = age

        @classmethod
        def fromBirthYear(cls, name, birthYear):
            return cls(name, date.today().year - birthYear)

        def display(self):
            print(self.name + "'s age is: " + str(self.age))

    person = Person("Adam", 19)
    person.display()

    person1 = Person.fromBirthYear("John", 1985)
    person1.display()
    #########################################
    from datetime import date

    class Person:
        def __init__(self, name, age):
            self.name = name
            self.age = age

        @staticmethod
        def fromFathersAge(name, fatherAge, fatherPersonAgeDiff):
            return Person(name, date.today().year - fatherAge + fatherPersonAgeDiff)

        @classmethod
        def fromBirthYear(cls, name, birthYear):
            return cls(name, date.today().year - birthYear)

        def display(self):
            print(self.name + "'s age is: " + str(self.age))

    class Man(Person):
        sex = "Male"

    man = Man.fromBirthYear("John", 1985)  # return class man
    print(isinstance(man, Man))  # T

    man1 = Man.fromFathersAge("John", 1965, 20)  # return class person
    print(isinstance(man1, Man))  # F


def staticmethod_keyword():
    class Calculator:

        def add_numbers(num1, num2):
            return num1 + num2

    Calculator.add_numbers = staticmethod(Calculator.add_numbers)
    sum = Calculator.add_numbers(5, 7)  # mà không cần tạo đối tượng của lớp.
    print("Sum:", sum)

    ###########################
    class Dates:
        def __init__(self, date):
            self.date = date

        def getDate(self):
            return self.date

        @staticmethod
        def toDashDate(date):
            return date.replace("/", "-")

    class DatesWithSlashes(Dates):
        def getDate(self):
            return Dates.toDashDate(self.date)

    date = Dates("15-12-2016")
    dateFromDB = DatesWithSlashes("15/12/2016")

    if date.getDate() == dateFromDB.getDate():
        print("Equal")
    else:
        print("Unequal")


def statticmethod_classmethod():
    from datetime import date

    class Person:
        def __init__(self, name, age):
            self.name = name
            self.age = age

        @staticmethod
        def fromFathersAge(name, fatherAge, fatherPersonAgeDiff):
            return Person(name, date.today().year - fatherAge + fatherPersonAgeDiff)

        @classmethod
        def fromBirthYear(cls, name, birthYear):
            return cls(name, date.today().year - birthYear)

        def display(self):
            print(f"{self.name}'s age is: {self.age}")

    class Man(Person):
        sex = "Male"

    # Sử dụng class method để tạo đối tượng Man
    man = Man.fromBirthYear("John", 1985)
    print(isinstance(man, Man))  # Output: True

    # Sử dụng static method để tạo đối tượng Person
    man1 = Man.fromFathersAge("John", 1965, 20)
    print(isinstance(man1, Man))  # Output: False


class MyClass:
    def __init__(self, value):
        self._value = value

    @property
    def ad(self):
        return self._value

    @ad.setter
    def value(self, new_value):
        self._value = new_value

    @value.deleter
    def de(self):
        del self._value


a = MyClass(123)
a.de
print(a._value)
