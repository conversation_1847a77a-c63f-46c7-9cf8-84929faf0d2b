import math
import itertools
import datetime
from operator import itemgetter

today = datetime.date.today()
print(f"Hôm nay là ngày {today:%d %m, %Y}")


def __radd__(self, other):  # c3 = 1 + c2
    if isinstance(other, int):
        # return Count(self._count + other)
        pass
    else:
        return self.__add__(other)


def __iadd__(self, other):  # c2 += c3
    self._count = self._count + other._count
    return self._count


def module_itertools():

    # count: Sinh dãy số vô hạn từ giá trị start, bước nhảy là step
    for number in itertools.count(start=1, step=2):
        if number > 20:
            break
        print(number, end=" ")

    print("=" * 50)

    # cycle: Lặp lại một chuỗi vô hạn
    for index, i in enumerate(itertools.cycle("ABCD")):
        if int(index) > 7:
            break
        else:
            print(i, index, sep="=", end=" ")

    print("=" * 50)

    # cycle: Lặp lại chuỗi lặp vô hạn
    l = ["Geeks", "for", "Geeks"]
    iterators = itertools.cycle(l)
    for i in range(6):
        print(next(iterators), end=" ")

    print("=" * 50)

    # repeat: Lặp lại một đối tượng n lần
    print(list(itertools.repeat([123, 123, 23, 3], 4)))
    print(list(itertools.repeat({"asd": "value"}, 4)))
    print(list(itertools.repeat("asd", 4)))
    print(list(itertools.repeat(123.54, 4)))

    print("=" * 50)

    # product: Sinh phép toán tích Cartesian (chỉnh hợp lặp)
    print(list(itertools.product([1, 2], repeat=3)))
    print(list(itertools.product(["geeks", "for", "geeks"], ["asd", "aes"])))
    print(list(itertools.product("AB", [3, 4])))

    print("=" * 50)

    # permutations: Sinh các hoán vị của các phần tử trong iterable
    print(list(itertools.permutations([1, "geeks", 1.234, ["ha"]], 3)))
    print(list(itertools.permutations("ABC")))  # 3P3
    print(list(itertools.permutations(range(3), 2)))

    print("=" * 50)

    # combinations: Tổ hợp không lặp (chọn k phần tử từ n phần tử)
    print(list(itertools.combinations(["A", 2, 3.14, ("haha")], 2)))
    print(list(itertools.combinations("ABCD", 2)))  # 4C2
    print(list(itertools.combinations(range(3), 2)))

    print("=" * 50)

    # combinations_with_replacement: Tổ hợp có lặp
    print(list(itertools.combinations_with_replacement([1, 2], 3)))
    print(list(itertools.combinations_with_replacement("AB", 4)))
    print(list(itertools.combinations_with_replacement(range(3), 2)))

    print("=" * 50)

    # chain: Nối nhiều iterable lại với nhau
    li1 = [1, 4, 5, 7]
    li2 = [1, 6, 5, 9]
    li3 = ["a", "b", "c"]
    li4 = [li1, li2, li3]
    print(list(itertools.chain(li1, li2, li3)))  # Nối ba danh sách lại
    print(li1 + li2 + li3)
    print(
        list(itertools.chain.from_iterable(li4))
    )  # Phá vỡ các danh sách con trong li4

    print("=" * 50)

    # compress: Lọc giá trị theo điều kiện trong iterable
    print("The compressed values in string are : ", end="")
    print(
        list(
            itertools.compress(
                "GEEKSFORGEEKS", [1, 0, 0, 123, 0, 0, 0, 0, 0, 0, 0, 0, 0]
            )
        )
    )

    print("=" * 50)

    # dropwhile: Bỏ qua các phần tử thỏa mãn điều kiện đến khi gặp giá trị đầu tiên không thỏa mãn
    li = [2, 4, 2, 6, 8, 5, 7, 8, 9, 10, 13, 12]
    print(list(itertools.dropwhile(lambda x: x % 2 == 0, li)))

    # filterfalse: Trả về những phần tử không thỏa mãn điều kiện
    print(list(itertools.filterfalse(lambda x: x % 2 == 0, li)))

    print("=" * 50)

    # starmap: Áp dụng một hàm vào các phần tử từ iterable theo tuple
    li = [(1, 10, 5), (8, 4, 1), (5, 4, 9), (11, 10, 1)]
    print(list(itertools.starmap(max, li)))

    print("=" * 50)

    # zip_longest: Nối các iterable, điền giá trị khi một iterable hết phần tử
    print(*(itertools.zip_longest("Gee", "ekfrek", fillvalue="_")))

    counter = itertools.count(start=10, step=2)
    for _ in range(5):
        print(next(counter))

    cycler = itertools.cycle("ABCD")
    for _ in range(8):
        print(next(cycler))

    repeater = itertools.repeat("Hello", 3)
    for item in repeater:
        print(item)

    chainer = itertools.chain([1, 2, 3, 5, 6], ["a", "b", "c"])
    for item in chainer:
        print(item)
    print("=" * 50)
    data = "ABCDE"
    selectors = [1, 0, 1, 2, 1]
    compressed = itertools.compress(data, selectors)
    for item in compressed:
        print(item)
    print("=" * 50)
    data = [1, 2, 3, 4, 5, 6]
    result = itertools.dropwhile(lambda x: x < 4, data)  # filter
    for item in result:
        print(item)
    print("=" * 50)
    data = [1, 2, 3, 4, 5, 6]
    result = itertools.takewhile(lambda x: x < 4, data)  # filter
    for item in result:
        print(item)
    print("=" * 50)
    result = itertools.permutations("ABC", 2)  # chinh hop
    for item in result:
        print(item)
    print("=" * 50)
    result = itertools.combinations("ABC", 2)  # to hop
    for item in result:
        print(item)

    numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    mybathch = itertools.batched(numbers, 3)
    print(*mybathch)  # (1,2,3) , (4,5,6) , (7,8,9) , (10,)

    name = ["ana", "bam", "cam", "dam"]
    num = [1, 2, 3, 4, 5, 6]
    symbol = ["!", "@", "#", "%", "&", "*"]
    zipp = itertools.zip_longest(name, num, symbol, fillvalue="???")
    print(list(zipp))

    ele = ["A", "B", "C"]
    my_pro = itertools.product(ele, repeat=2)
    print(list(my_pro))

    def get_sum(*agrs):
        return sum(agrs)

    data = [(1, 2, 3), (4, 5, 6)]
    sums = itertools.starmap(get_sum, data)
    print(list(sums))
    data = [(1, 2), (4, 5)]
    sums = itertools.starmap(pow, data)
    print(list(sums))

    def count_wowle(word):
        woevel_count = 0
        for letter in word:
            if letter in "aeiouAEIOU":
                woevel_count += 1
        return woevel_count

    words = ["cat", "dog", "modd", "lol", "red", "banana", "orange", "apple", "mango"]
    sort_word = sorted(words, key=count_wowle)
    group_word = itertools.groupby(sort_word, key=count_wowle)
    print(sort_word)
    for key, value in group_word:
        print(key, list(value))


# max float: 10**308.254
if __name__ == "__main__":
    pass


def assert_keyword():

    def calculate_rectangle_area(length, width):
        assert length > 0 and width > 0, "so phai duong"
        area = length * width
        return area

    area1 = calculate_rectangle_area(5, 6)
    print("Area of rectangle with length 5 and width 6 is", area1)

    ass = "hello"
    assert type(ass) == int, " error type"

    ass_dict = {"apple": 1, "banana": 2, "cherry": 3}
    print("My dictionary contains the following key-value pairs:", ass_dict)
    assert ass_dict["apple"] == 1

    batch = [40, 26, 39, 30, 25, 21]
    for i in batch:
        assert i >= 22, f"Batch {i} is Rejected"
        print(str(i) + " is O.K")


def zipp():
    fruits = ["apple", "banana", "cherry", "asd", "sss"]  # dư thì skip
    colors = ["red", "yellow", "green", "efg"]
    for i, (fruit, color) in enumerate(zip(fruits, colors)):
        print(i, fruit, "is", color, end="\t ||")

    stocks = ["GEEKS", "For", "geeks"]
    prices = [1111, 2222, 3333]
    new_dict = {stocks: prices for (stocks, prices) in zip(stocks, prices)}
    for key, value in new_dict.items():
        print(f"{key}: {value}", end="\t ||")

    name = ["Manjeet", "Astha", "Shambhavi", "Astha", "ak"]
    id = [4, 1, 3, 1]
    mapped = list(zip(name, id))
    print(mapped)
    print(set(mapped))
    u_anme, u_id = zip(*mapped)  # giải nén
    print("Unique Names:", u_anme)


def ways_to_input():
    n = int(input("Enter the size of list : "))
    lst = list(map(int, input("Enter the integer elements:").strip().split()))[:n]
    print(lst)
    # c2
    try:
        my_list = []
        while True:
            my_list.append(int(input()))

    except:
        print(my_list)
    # c3
    lst1 = [int(item) for item in input("Enter  the list items : ").split()]
    lst2 = [item for item in input("Enter  the list items : ").split()]
    print(lst1)
    print(lst2)


def myFun(*args, **kwargs):
    for key, value in kwargs.items():
        print("%s == %s" % (key, value))
    for i in args:
        print(i)

    myFun("Hi", "heelo", "bonjour", first="Geeks", mid="for", last="Geeks")


def myFun(arg1, arg2, arg3):
    print("arg1:", arg1)
    print("arg2:", arg2)
    print("arg3:", arg3)

    args = ["Geeks", "for", "Geeks"]
    myFun(*args)
    kwargs = {"arg1": "Geeks", "arg2": "for", "arg3": "Geeks"}
    myFun(**kwargs)


def print_movie(**kwargs):
    for key, value in kwargs.items():
        print(f"{key}: {value}")

    movie = {"title": "The Matrix", "director": "Wachowski", "year": 1999}
    print_movie(studio="Warner Bros", **movie)


def lamda_keyword():
    print((lambda x, y: x * y)(3, 4))  # 12
    items = [1, 2, 3, 4, 5]
    cubes = list(map(lambda x: x**3, items))  # 1 8 27 64 125
    number_list = range(-10, 10)
    less_than_five = list(filter(lambda x: x < 5, number_list))  # -10...4
    x = [2, 3, 4, 5, 6]
    y = list(
        map(lambda v: v * 5, filter(lambda u: u % 2, x))
    )  # lấy số lẻ nhân 5 : 15 25

    words = ["apple", "banana", "cherry", "date"]
    sorted_words = sorted(words, key=lambda x: len(x))  # date apple banana cherry

    List = [[2, 3, 4], [1, 4, 16, 64], [3, 6, 9, 12]]
    sortList = lambda x: (sorted(i) for i in x)  # sắp xếp min..max
    secondLargest = lambda x, f: [y[len(y) - 2] for y in f(x)]
    res = secondLargest(List, sortList)  # 3 16 9


def yield_keyword():
    def fibonacci():
        a, b = 0, 1
        while True:
            yield a
            a, b = b, a + b

    fib = fibonacci()
    for _ in range(10):
        print(next(fib), end="\t")  # 1 1 2 3 5 8 13 21 34 55
        pass

    def read_file_line_by_line(file_path):
        with open(file_path, "r") as file:
            for line in file:
                yield line.strip()

    for line in read_file_line_by_line("SGK.txt"):
        print(line + "||||")


def frozenset_keyword():
    A = frozenset([1, 2, 3, 4, 3, 2, 1])
    B = frozenset([3, 4, 5, 6])
    setA = set(A)
    C = A.copy()
    print(A.union(B))  # A| B
    print(A.intersection(B))  # A & B
    print(A.difference(B))  # A-B
    print(A.symmetric_difference(B))  # (A ^ B) : not intersection


def set_keyword():
    people = {"A", "B"}
    vampires = ["1", "2"]
    dracula = {"one", "two"}
    print(people.pop())  # random
    people.add("C")
    # intersection // difference //  symmetric_difference: lấy phần không giao  có thể kết hơp vs list
    population = people.union(vampires)
    print("Union using union() function")
    print(population)
    # & // - // ^  // toán tử không kết hợp với list
    population = population | dracula
    print("\nUnion using '|' operator")
    print(population)
    population.clear()


def dict_keyword():

    Dict = dict({1: "Geeks", 2: "For", 3: "Geeks"}, z=8)  # dict+other
    Dict = dict([(1, "Geeks"), (2, "For")], z=9)  # list (tuple) + other
    Dict = dict(((1, "Geeks"), (2, "For")), z=9)  # tuple(tuple)+other
    numbers = dict(x=5, y=0)
    numbers3 = dict(zip(["x", "y", "z", 2], [1, 2, 3, 200]))
    numbers2 = {"x": 4, "y": 5, 5: 6}

    Dict = {}
    Dict[0] = "Geeks"
    Dict[2] = "For"
    Dict[3] = 1
    Dict["Value_set"] = 2, 3, 4
    Dict[5] = {"Nested": {"1": "Life", "2": "Geeks"}}
    print(Dict[5]["Nested"]["1"])
    print(Dict.get(5).get("net"))
    print(Dict.items())
    print(Dict.keys())
    print(Dict.values())
    Dict.update({3: "Scala"})
    p = Dict.pop(3)
    print("pop item:", p)
    print("################################")
    seq = ("a", "b", "c")
    seq = {"a", "b", "c", "d", "e"}
    seq = ["a", "b", "c", "d", "e"]
    tu = [12, 3]
    print(dict.fromkeys(seq, tu))  # có tham chiếu, thay đổi
    res_dict2 = {key: (tu) for key in seq}  # có tham chiếu, thay đổi
    tu.append(99)
    print(dict.fromkeys(seq, tu))
    print(res_dict2)
    print("################################")
    test_dict = {"Geeks": 7, "for": 1, "geeks": 2}
    print("2nd key using keys() : ", list(test_dict.keys())[1])
    test_dict.update({9.98: 99, "Hello": "Bonjour"})  # cùng type :
    test_dict.update(hi=98)  # khác type =
    test_dict.update([("id", " 123"), {"mssv", "2033"}])  # test_dict | info
    a = test_dict.popitem()  # lấy cái cuối
    nu = test_dict.setdefault("a", "h")  # => vl, if not create key-value => new vl
    data_list = [
        {"name": "Nandini", "age": 20},
        {"name": "Manjeet", "age": 20},
        {"name": "Nikhil", "age": 19},
    ]
    print(sorted(data_list, key=itemgetter("age", "name")))


def memory_view():
    """tiết kiếm bộ nhớ, không cần phải sao chép khi làm dữ liệu lớn, chỉnh sửa trên memory view"""
    byte_array = bytearray("XYZ", "utf-8")
    print("Before update:", byte_array)
    mem_view = memoryview(byte_array)
    print(type(mem_view))
    mem_view[2] = 74
    print("After update:", byte_array)
    ######################################################
    byt = bytes(mem_view)
    print(type(byt))

    import numpy as np

    large_array = np.arange(1e7, dtype=np.int32)  # 0... 9999999999999
    print(large_array)
    # Tạo một khung nhìn bộ nhớ từ mảng numpy
    large_view = memoryview(large_array)

    # Truy cập và sửa đổi dữ liệu trực tiếp
    print("Before:", large_array[0:10])
    large_view[:10] = np.arange(10, 20)
    print("After:", large_array[0:10])


def bytearray_keyword():
    str = "Geeksforgeeks"
    array1 = bytearray(str, "utf-8")
    array2 = bytearray(str, "utf-16")
    print(array1, array2)
    #########################
    size = 3
    array1 = bytearray(size)
    print(array1)
    ###############
    arr1 = bytearray(b"abcd")
    for value in arr1:
        print(value)
    arr2 = bytearray(b"aaaacccc")
    print("Count of c is:", arr2.count(b"c"))
    ##############
    list = [1, 2, 3, 4]
    array = bytearray(list)
    print(array, len(array))
    ###################


def byte_type():
    arr = bytes("Welcome to Geeksforgeeks", "utf-8")
    print(arr)
    result = bytes(12)
    print(result)
    ################################################################
    lis1 = [1, 2, 3, 4, 5]
    print("The iterable conversion results in : " + str(bytes(lis1)))
    ########################################################

    str1 = "GeeksfÖrGeeks"

    # Giving ascii encoding and ignore error
    print(" ignore error : " + str(bytes(str1, "ascii", errors="ignore")))  # ->skip
    print(
        "replace error : " + str(bytes(str1, "ascii", errors="replace"))
    )  # replace '?'
    print("strict error : " + str(bytes(str1, "ascii", errors="strict")))  # -> error
    # xmlcharrefreplace  backslashreplace    namereplace


def list_comprehension():
    noprimes = [j for i in range(2, 8) for j in range(i * 2, 50, i)]
    primes = [x for x in range(2, 50) if x not in noprimes]
    print([x.lower() for x in ["A", "B", "C"]])
    string = "my phone number is : 11122 !!"
    numbers = [x for x in string if x.isdigit()]
    print(numbers)
    a = 5
    table = [[a, b, a * b] for b in range(1, 6)]  # [5, 1, 5] ,[5, 2, 10]
    # kết hợp với lambda #############
    lst = filter(lambda x: x % 2 == 1, range(1, 20))
    # [1, 3, 5, 7, 9, 11, 13, 15, 17, 19]
    lst = filter(lambda x: x % 5 == 0, [x**2 for x in range(1, 21) if x % 2 == 1])
    # [25 ,225]
    lst = filter((lambda x: x < 0), range(-5, 5))
    # [-5, -4, -3, -2, -1]
    numbers = list(map(lambda i: i * 10, [i for i in range(1, 6)]))
    # [10, 20, 30, 40, 50]
    is_even_list = [lambda arg=x: arg * 10 for x in range(1, 5)]
    # 10 20 30 40


def reduce_keyword():
    import functools
    import itertools

    lis = [1, 3, 5, 6, 2]
    print(functools.reduce(lambda a, b: a + b, [], 15))  # empty thif laays agr 3
    print(functools.reduce(lambda a, b: a + b, lis, 15))  # not empty return +agr3
    # trả về giá trị tổng cuối cùng
    print(functools.reduce(lambda a, b: a + b, lis))
    # trả về từng giá trị sau khi tính từ trái qua phải
    print(list(itertools.accumulate(lis, lambda x, y: x + y)))
    print(functools.reduce(lambda a, b: a if a > b else b, lis))
    print(functools.reduce(lambda a, b: a + b, ["geeks", "for", "geeks"]))


def property_keyword():
    class Alphabet:
        def __init__(self, value):
            self._value = value

        def get(self):
            print("Getting value")
            return self._value

        def set(self, value):
            print("Setting value to " + value)
            self._value = value

        def dele(self):
            print("Deleting value")
            del self._value

        value = property(
            get,
            set,
            dele,
        )

    x = Alphabet("GeeksforGeeks")
    print(x.value)
    x.value = "GfG"
    del x.value

    class Alphabet:
        def __init__(self, value):
            self._value = value

        @property
        def value(self):
            print("Getting value")
            return self._value

        @value.setter
        def value(self, value):
            print("Setting value to " + value)
            self._value = value

        @value.deleter
        def value(self):
            print("Deleting value")
            del self._value

    x = Alphabet("Peter")
    print(x.value)

    x.value = "Diesel"
    print(x.value)

    del x.value


def closure():
    def make_multiplier_of(n):
        def multiplier(x):
            return x * n

        return multiplier

    # Multiplier of 3
    times3 = make_multiplier_of(3)

    # Multiplier of 5
    times5 = make_multiplier_of(5)

    # Output: 27
    print(times3(9))
    print(times3)
    # Output: 15
    print(times5(3))

    # Output: 30
    print(times5(times3(2)))
