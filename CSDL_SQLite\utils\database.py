from utils.database_connection import DatabaseConnection

HOST = "books.db"
TABLE = "book"
SQL_CREATE_TABLE = f""" CREATE TABLE IF NOT EXISTS {TABLE} (
title text not null, 
author text not null,
year integer not null);
"""
SQL_INSERT_TABLE = f""" INSERT INTO {TABLE} (title, author, year) VALUES (?, ?, ?);"""
SQL_SELECT_TABLE = f""" SELECT * FROM {TABLE};"""
SQL_UPDATE_TABLE = f""" UPDATE {TABLE} 
    SET  author = ?, year = ? 
    WHERE title = ?;"""
SQL_DELETE_TABLE = f""" DELETE FROM {TABLE} 
WHERE title = ?;"""


def create_book_table():
    with DatabaseConnection(HOST) as conn:
        cur = conn.cursor()
        cur.execute(SQL_CREATE_TABLE)


def get_all_books():
    with DatabaseConnection(HOST) as conn:
        cur = conn.cursor()
        cur.execute(SQL_SELECT_TABLE)
        for title, author, year in cur.fetchall():
            print(f"Title: {title}, Author: {author}, Year: {year}")


def input_book():
    title = input("Enter title: ").title()
    author = input("Enter author: ")
    year = int(input("Enter year: "))
    return title, author, year


def insert_book():
    with DatabaseConnection(HOST) as conn:
        cur = conn.cursor()
        cur.execute(SQL_INSERT_TABLE, input_book())


def update_book():
    title, author, year = input_book()
    with DatabaseConnection(HOST) as conn:
        cur = conn.cursor()
        cur.execute(SQL_UPDATE_TABLE, (author, year, title))


def delete_book():
    title = input("Enter title: ").title()
    with DatabaseConnection(HOST) as conn:
        cur = conn.cursor()
        cur.execute(SQL_DELETE_TABLE, (title,))
