from matplotlib import pyplot as plt
import pandas as pd

bestsellers = pd.read_csv("bestsellers.csv")
titanic = pd.read_csv("titanic.csv")
countries = pd.read_csv("world-happiness-report-2021.csv")
movies = pd.read_csv("netflix_titles.csv")
houses = pd.read_csv("kc_house_data.csv")
pokemons = pd.read_csv("Pokemon.csv")
pokemons.set_index("Name", inplace=True)
pokemons.sort_index(inplace=True)
# ===============================================COUNTRIES========================================================
# countries.set_index("Country name", inplace=True)
# countries.set_index("Regional indicator", inplace=True)  # mất 2 cột
# print(countries.columns)
# print(countries)
# print(countries.sort_index())
# print(countries["Ladder score"].head().sort_values())
# plt.show()
# print(countries.loc["Vietnam"])
# print(countries.head().loc[[True, False, False, False, True]])
# print(countries.iloc[0:3])  # loc include  stop, iloc don't
# ===============================================================================================================
# a = houses.sort_values(by="price", ascending=False)
# print(a)
# a = titanic.sort_values("name", key=lambda x: x.str.lower())
# print(a)
# =================================================POKEMON===============================================================
# print(pokemons.sort_values(by="Attack", ascending=False))
# print(pokemons.sort_values(by="#"))
# print(pokemons.sort_values(by=["Total", "Attack"], ascending=[False, False]))
# print(pokemons.sort_index())
# print(pokemons["Speed"].nlargest(10).mean())
# print(pokemons.sort_values(by="Attack", ascending=False).head(20)["Type 1"].mode()[0])
# print(pokemons.loc["Diglett"])
# print(pokemons.loc[["Eevee", "Vulpix"]])
# print(pokemons.loc["Zoroark":"Zweilous"])
# print(pokemons.iloc[[30, 40, 50]])
# pokemons[pokemons["Legendary"] == True]["Attack"].sort_values().plot(kind="barh")
# plt.show()
# ====================================================FILLTER================================================================
# print(titanic.info())
# titanic["age"] = titanic["age"].astype(str)# convert to string
# print(titanic.columns)'pclass', 'survived', 'name', 'sex', 'age', 'sibsp', 'parch', 'ticket','fare', 'cabin', 'embarked', 'boat', 'body', 'home.dest'
# print(titanic[titanic["sex"] == "male"])
# print(titanic[titanic["age"].isnull()])  #  == "NaN"
# print(titanic[(titanic["age"] > 30) & ~(titanic["survived"] == 1)])
# print(titanic[titanic["age"].between(20, 30)])
# print(movies.rating.value_counts())
# print(movies[movies["rating"].isin(["66 min", "84 min", "74 min"])])
# print(movies[movies["director"].isna()])
# print(movies[movies["director"].notna()])
# print(titanic[titanic.sex == "male"].survived.value_counts().plot(kind="bar"))
# plt.show()

# print(bestsellers[bestsellers["User Rating"] < 4.5]["Author"].value_counts().head())
import pandas as pd

# Tạo DataFrame mẫu
data = {
    "Name": ["Alice", "Bob", "Charlie"],
    "Age": [25, 30, 28],
    "City": ["New York", "London", "Paris"],
}
df = pd.DataFrame(data)
print("DataFrame gốc:\n", df)

# Thiết lập cột 'Name' làm chỉ mục
df_indexed = df.set_index("Name")
print("\nDataFrame sau khi set_index('Name'):\n", df_indexed)
df_indexed = df.set_index("Name", drop=False)
print("\nDataFrame sau khi set_index('Name', drop=False):\n", df_indexed)
df_multi_indexed = df.set_index(["Name", "City"])
print("\nDataFrame sau khi set_index(['Name', 'City']):\n", df_multi_indexed)
df_appended = df.set_index("Name", append=True)
print("\nDataFrame sau khi set_index('Name', append=True):\n", df_appended)
df.set_index("Name", inplace=True)
print("\nDataFrame sau khi set_index('Name', inplace=True):\n", df)
