# import streamlit as st

# # streamlit run app.py
# st.title("This is the title of our app")
# st.header("This is a Header")
# st.subheader("This is a Subheader")
# st.write("You can write text here and it will appear as a paragraph.")
# # ============================================================================================
# button = st.button("Click Me")
# if button:  # button is True if clicked
#     st.write("You clicked the button")

# # ============================================================================================

# text_file_content = (
#     "This is a sample text file. This content will be downloaded as a text file."
# )
# st.download_button(
#     label="Download Text File",
#     data=text_file_content,
#     file_name="sample.txt",
#     mime="text/plain",
# )
# # ============================================================================================
# choice = st.radio("Choose an option:", ["Option 1", "Option 2", "Option 3"])

# if choice == "Option 1":
#     st.write("You selected Option 1")
# elif choice == "Option 2":
#     st.write("You selected Option 2")
# else:
#     st.write("You selected Option 3")

# # ============================================================================================

# age = st.slider("Select your age:", 0, 100, 25, key="age")
# st.write(f"Your age is: {age}")

# # ============================================================================================


# start, end = st.slider("Select a range of values:", 0, 100, (20, 80))

# st.write(f"Selected range: {start} to {end}")
# # ============================================================================================
# fruit = st.selectbox(
#     "Select your favorite fruit:", ["Apple", "Banana", "Orange", "Grapes", "Mango"]
# )

# st.write(f"You selected: {fruit}")
# # ============================================================================================

# haha = st.sidebar.slider("Select your age:", 0, 100, 25, key="age2")
# st.write(f"Your age is: {haha}")

# # ============================================================================================
# color = st.sidebar.selectbox("Select a color:", ["Red", "Green", "Blue"])

# # Add a slider to the sidebar
# level = st.sidebar.slider("Select the intensity level:", 0, 100, 50)

# # Add a button to the sidebar
# if st.sidebar.button("Apply Settings"):
#     st.write(f"Settings applied: Color={color}, Level={level}")


import pandas as pd

# # ============================================================================================
# # ============================================================================================
# # ============================================================================================
# # ============================================================================================
# # ============================================================================================
# # ============================================================================================
# # ============================================================================================
# # ============================================================================================
# # ============================================================================================
# # ============================================================================================
# # ============================================================================================
# # ============================================================================================
# # ============================================================================================
import streamlit as st

# Load the Iris dataset
df = pd.read_csv("../../flower.csv")

# Set the title of the app
st.title("Iris Dataset Explorer")

# Display the entire dataframe
st.write("### Full Iris Dataset")
st.dataframe(df)

# Sidebar configuration
st.sidebar.header("Filter Options")

# Feature selection
feature = st.sidebar.selectbox("Select a feature to filter by:", df.columns[:-1])

# Range selection based on the selected feature
min_value = float(df[feature].min())
max_value = float(df[feature].max())

range_slider = st.sidebar.slider(
    f"Select range of {feature}:", min_value, max_value, (min_value, max_value)
)

# Filter the dataframe based on the selected range
filtered_df = df[(df[feature] >= range_slider[0]) & (df[feature] <= range_slider[1])]

# Display the filtered dataset
st.write(
    f"### Filtered Iris Dataset by {feature} between {range_slider[0]} and {range_slider[1]}"
)
st.dataframe(filtered_df)

# Display basic statistics for the filtered data
st.write(f"### Statistics for {feature}")
st.write(filtered_df[feature].describe())
