import utils.database as db

MENU = """
a - add book
l - list book
u - update book
d - delete book
q - quit
choice  >>  """
operations = {
    "a": db.insert_book,
    "l": db.get_all_books,
    "u": db.update_book,
    "d": db.delete_book,
    "q": exit,
}
db.create_book_table()

userchoice = input(MENU).lower()

while userchoice != "q":
    if userchoice in operations:
        operations[userchoice]()
    else:
        print("Invalid choice")
    userchoice = input(MENU).lower()
