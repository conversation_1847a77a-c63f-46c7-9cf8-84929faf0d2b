from matplotlib import pyplot as plt
import numpy as np
import pandas as pd

houses = pd.read_csv("kc_house_data.csv")
titanic = pd.read_csv("titanic.csv")
movies = pd.read_csv("netflix_titles.csv")
countries = pd.read_csv("world-happiness-report-2021.csv", index_col=0)
# ['SNo', 'Name', 'Symbol', 'Date', 'High', 'Low', 'Open', 'Close','Volume', 'Marketcap'
btc = pd.read_csv("coin_Bitcoin.csv")
# ================================================================================================================================
# countries.drop(labels="Finland", axis=0, inplace=True)
# countries.drop(index=["Finland", "Denmark"], inplace=True)
# countries.drop(["Finland", "Denmark"], inplace=True)
# countries.drop(countries.index[10:], inplace=True)
# countries["test"] = "test"
# countries.insert(0, "test2", "test2")
# print(countries.columns)
# mapper = {"Regional indicator": "Regional_indicator"}
# countries.rename(columns=mapper, inplace=True)
# countries.rename(index={"Finland": "finland"}, inplace=True)
countries.loc[["Finland", "Denmark"], ["Ladder score"]] = 7.888
# countries.loc[["Finland"], ["Regional indicator", "Ladder score"]] = "??"
print(countries)
# ================================================================================================================================
# titanic["num_relative"] = titanic["parch"] + titanic["sibsp"]
# solo = titanic["num_relative"] == 0
# print(titanic[solo]["sex"].value_counts().plot(kind="bar"))
# plt.show()
# titanic["sex"].replace("female", "F", inplace=True)
# titanic["sex"].replace("male", "M", inplace=True)
# titanic["sex"].replace(["male", "female"], ["M", "F"], inplace=True)
# print(titanic["age"].fillna("IDK").value_counts())

# ================================================================================================================================
# btc.drop("High", axis=1, inplace=True)
# a = btc.drop(columns=["High", "SNo"])
# print(a)
# print(btc.shape)
# btc.sort_index(ascending=False, inplace=True)
# btc.drop(2989, inplace=True)
# print(btc.shape)
# print(btc)
