import json
import requests


# Request line, Headers, và Body.
# GET /page HTTP/1.1
# Headers chứa các thông tin Content-Type, User-Agent, Accept, vv.
# Mỗi header được phân tách (\r\n) và có dạng Key: Value.
# Host: example.com
# User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
# like Gecko) Chrome/96.0.4664.110 Safari/537.36
# Accept:
# text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,i
# mage/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9
# Đối với phương thức GET, thông thường không có bodyexample
# POST, PUT, DELETE, body thường chứa dữ liệu được gửi đi.
# được mã hóa theo định dạng được chỉ định trong header Content-Type.
def vd():
    url = "https://jsonplaceholder.typicode.com/todos"
    repon = requests.get(url)
    print(repon.json())  # content, json(), header


def gui_HTTPGET_Request():
    url = "https://v1.slashapi.com/huit/mysql/po8PTHDOU6/products"
    response = requests.get(url)  # post put delete
    if response.status_code == 200:
        print("Dữ liệu nhận được:")
        print(response.text)
    else:
        print("Lỗi! Mã trạng thái:", response.status_code)


# gui_HTTPGET_Request()


def gui_HTTPPOST_Request():
    url = "https://httpbin.org/post"  # https://nghiait.wiremockapi.cloud/post
    data = {"Name": "Khanh", "Age": 18, "Country": "VN"}
    response = requests.post(url, data=data)
    print(response.status_code)
    if response.status_code == 200:
        print(response.text)
    else:
        print(response.status_code)


# gui_HTTPPOST_Request()


def truyenDL_quaURLparament():
    url = "https://api.example.com/data?"
    params = {"key1": "value1", "key2": "value2"}
    response = requests.get(url, params=params)
    print(response.status_code)
    print(response.text)


# truyenDL_quaURLparament()


def truyenDL_quabodycuaRQ():
    url = "https://api..com/post_data"
    data = {"key1": "value1", "key2": "value2"}
    response = requests.post(url, data=data)

    url = "https://api.example.com/post_json"
    json_data = {"key1": "value1", "key2": "value2"}  # them truong DL neu co
    json_data1 = {"data": {"key1": "value1", "key2": "value2"}}
    response = requests.post(url, json=json_data)


# truyenDL_quabodycuaRQ()


def truyenDL_quahead():
    url = "https://api.example.com/data"
    headers = {"Authorization": "Bearer my_access_token"}
    response = requests.get(url, headers=headers)


# truyenDL_quahead()


def themheadvaoreq():
    # URL của endpoint bạn muốn gửi Request đến
    url = "https://nghiait.wiremockapi.cloud/data"
    # Định nghĩa các headers bạn muốn thêm vào Request
    headers = {
        "User-Agent": "My User Agent",
        "Authorization": "Bearer my_access_token",
        "Content-Type": "application/json",
    }
    # Gửi HTTP GET Request với các headers đã xác định
    response = requests.get(url, headers=headers)
    # print(response.text)


# themheadvaoreq()


def nhan_cookietuResponse():
    url = "https://api.github.com/"
    response = requests.get(url)
    # Truy cập cookies từ Response
    cookies = response.cookies
    print(response.status_code, cookies)


def gui_cookiestrongRQ():
    url = "https://nghiait.wiremockapi.cloud"
    # Định nghĩa cookies bạn muốn gửi
    cookies = {"cookie_name": "cookie_value"}
    response = requests.get(url, cookies=cookies)
    print(response.status_code, response.text)


# gui_cookiestrongRQ()


def duytri_cookiesgiuacacRQ():
    url = "https://example.com"
    # Tạo một đối tượng Session
    session = requests.Session()
    # Gửi Request bằng cách sử dụng Session
    response = session.get(url)
    # Request tiếp theo sẽ tự động gửi cookies đã nhận được từ Response trước đó
    response = session.get(url)
    # print(response.text)


# duytri_cookiesgiuacacRQ()

# url = "https://v1.slashapi.com/khanh/mysql/OeUxJFy3bo"
url = "https://v1.slashapi.com/huit/mysql/po8PTHDOU6/products"
# url="https://v1.slashapi.com/khanh/mysql/OeUxJFy3bo"
response = requests.get(url)
print(response.status_code)


def Show_info():
    if response.status_code == 200:
        print("Dữ liệu nhận được:")
        # print(response.text)
        data = response.json()
        # data = json.loads(response.text)
        print(data)
        # Kiểm tra xem 'data' key có tồn tại trong phản hồi hay không
        if "data" in data:
            # Iterate over the products and display ID and Name
            for product in data["data"]:
                print("ID:", product.get("ID"), end=" ")
                print("Name:", product.get("Name"))
        else:
            print("No data found in the response.")
    else:
        print("Failed to fetch data from the API. Status code:", response.status_code)


# Show_info()
def find(id: int):
    if response.status_code == 200:
        # print(response.text)
        print("Dữ liệu nhận được:")
        data = response.json()
        # data = json.loads(response.text)
        print(data)
        # Kiểm tra xem 'data' key có tồn tại trong phản hồi hay không
        # if "data" in data:
        # Iterate over the products and display ID and Name
        print("=" * 80)
        for product in data["data"]:
            print(product)  # danh sach SP
            if product.get("ID") == id:
                print("ID:", product.get("ID"))
                print("Name:", product.get("Name"))
                print("Description:", product.get("Description"))
                print("Price:", product.get("Price"))
                break
            else:
                print("No data found in the response.")
    else:
        print("Failed to fetch data from the API. Status code:", response.status_code)


# find(213)


def add_product():
    data = {"data": {"Name": "Kah", "Description": "K13", "Price": 100}}
    # data = {"fact": "Dang Van Thai", "length": 98765}
    headers = {
        "User-Agent": "MyApp/1.0"
    }  # "Content-Type": "application/json" | "Authorization": "Bearer <access_token>"
    response = requests.post(url, json=data, headers=headers)
    if response.status_code == 200:
        repon_data = response.json()
        print(f"Product added successfully! ID: {response.json().get('ID')}")
        print(repon_data)
        # product_id = repon_data["id"]
        # print(f"ID mới: {product_id}")
    else:
        print(f"Error adding product. Status code: {response.status_code}")
        print(f"text: {response.text}")

    # he = requests.get(url)
    # if he.status_code == 200:
    #     products = he.json()
    #     print(he.status_code)
    # else:
    #     print(f"Error retrieving products. Status code: {he.status_code}")
    #     print(f"Text: {he.text}")


# add_product()
def delete(id):
    url = f"https://v1.slashapi.com/huit/mysql/po8PTHDOU6/products/{id}"
    headers = {"User-Agent": "MyApp/1.0"}  # Add any other headers if needed
    response = requests.delete(url, headers=headers)
    if response.status_code == 200:
        print(f"Product with ID {id} deleted successfully!")
    else:
        print(
            f"Error deleting product with ID {id}. Status code: {response.status_code}"
        )
        print(f"Response text: {response.text}")


# delete()
def update(id, data):
    url = f"https://v1.slashapi.com/huit/mysql/po8PTHDOU6/products/{id}"
    headers = {"User-Agent": "MyApp/1.0"}  # Add any other headers if needed
    response = requests.patch(url, json=data, headers=headers)
    if response.status_code == 200:
        print(f"Product with ID {id} updated successfully!")
    else:
        print(
            f"Error updating product with ID {id}. Status code: {response.status_code}"
        )
        print(f"Response text: {response.text}")


new_product_data = {
    "data": {
        f"Name": "Kah",
        "Description": "K13BM0x",
        "Price": 20332220,
    }
}
# update(1, new_product_data)
def delete_all():
    url = "https://v1.slashapi.com/huit/mysql/po8PTHDOU6/products"
    headers = {"User-Agent": "MyApp/1.0"}  # Add any other headers if needed
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        products = response.json()  # Convert response to JSON
        if "data" in products:
            # Iterate over the products and display ID and Name
            for product in products["data"]:
                product_id = product.get('ID')
                delete(product_id)
        else:
            print("No data found in the response.")
    else:
        print("Failed to fetch data from the API. Status code:", response.status_code)
        
# add_product()
# Show_info()
# delete_all()

