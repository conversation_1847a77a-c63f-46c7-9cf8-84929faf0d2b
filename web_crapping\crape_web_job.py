import requests
from bs4 import BeautifulSoup


def scrape_job_web():
    URL = "https://realpython.github.io/fake-jobs/"
    page = requests.get(URL)
    # print(page.text)
    soup = BeautifulSoup(page.content, "html.parser")
    #############################################################################
    results = soup.find(id="ResultsContainer")
    # print(results.prettify())
    #################################################################################
    job_cards = results.find_all("div", class_="card-content")
    # for job_card in job_cards:
    #     title_element = job_card.find("h2", class_="title")
    #     company_element = job_card.find("h3", class_="company")
    #     location_element = job_card.find("p", class_="location")
    #     print(title_element.text)
    #     print(company_element.text)
    #     print(location_element.text)
    #     # print(job_card.prettify(), end="\n" * 2)
    #     break

    python_jobs = results.find_all(
        "h2", string=lambda text: "python" in text.lower())
    # print(python_jobs)
    python_job_cards = [
        h2_element.parent.parent.parent for h2_element in python_jobs]

    for job_card in python_job_cards:
        title_element = job_card.find("h2", class_="title")
        company_element = job_card.find("h3", class_="company")
        location_element = job_card.find("p", class_="location")
        print(title_element.text)
        print(company_element.text)
        print(location_element.text.strip())
        links = job_card.find_all("a")[1]["href"]
        print(links)
        # print(job_card.prettify(), end="\n" * 2)
        break
    # python_job_cards = [
    #     h2_element.parent.parent.parent for h2_element in python_jobs
    # ]

    # for job_card in python_job_cards:
    #     title_element = job_card.find("h2", class_="title")
    #     company_element = job_card.find("h3", class_="company")
    #     location_element = job_card.find("p", class_="location")
    #     print(title_element.text.strip())
    #     print(company_element.text.strip())
    #     print(location_element.text.strip())
    #     link_url = job_card.find_all("a")[1]["href"]
    #     print(f"Apply here: {link_url}\n")

    # https://www.python.org/jobs/
    # https://pythonjobs.github.io/
    # https://remote.co/remote-jobs/developer/
    # lọc theo từ khóa
