# BeautifulSoup(xml_string, 'lxml-xml'): Khởi tạo đối tượng BeautifulSoup với nội dung XML.
# soup.find(tag): Tìm phần tử đầu tiên với thẻ cụ thể.
# soup.find_all(tag): <PERSON><PERSON><PERSON> tất cả các phần tử với thẻ cụ thể.
# soup.get_text(): Lấy nội dung văn bản bên trong phần tử.
# element['attribute']: T<PERSON>y cập giá trị của thuộc tính cụ thể.
# soup.prettify(): Chuyển đổi đối tượng BeautifulSoup thành chuỗi được định dạng dễ đọc.

# .string: return str , nếu có 1 tag return None:  <description>Grilled salmon with <highlight>fresh herbs</highlight></description>
# .text:return str, nối các phần tử, loại bỏ câc thẻ:   "Grilled salmon with fresh herbs"
# .contents: return list, bao gồm str và tag.: ['Grilled salmon with ', <highlight>fresh herbs</highlight>]
from bs4 import BeautifulSoup


def read_example():
    # Đọc file XML
    with open("example.xml", "r", encoding="utf-8") as file:
        content = file.read()  # str
    print(content)
    soup = BeautifulSoup(content, "lxml-xml")

    # Tìm và in tất cả các mục item
    items = soup.find_all("item")
    for item in items:
        name = item.find("name")
        price = item["price"]
        ingredients = [ingredient.string for ingredient in item.find_all("ingredient")]
        # ingredients = [ingredient.get_text() for ingredient in item.find_all("ingredient")]
        print(f"Name: {name}")
        print(f"Price: {price}")
        print(f"Ingredients: {', '.join(ingredients)}")
        print()


# read_example()


def example():
    note = """
    <?xml version="1.0" encoding="UTF-8"?>
<breakfast_menu>
    <food>
        <name>Belgian Waffles</name>
        <price>$5.95</price>
        <description>Two of our famous Belgian Waff
            les with plenty of real maple syrup</description>
        <calories>650</calories>
    </food>
    <food>
        <name>Strawberry Belgian Waffles</name>
        <price>$7.95</price>
        <description>Light Belgian waffles covered
            with strawberries and whipped cream</description>
        <calories>900</calories>
    </food>
</breakfast_menu>
    """
    soup = BeautifulSoup(note, "xml")
    foods = soup.findAll("food")
    for x in foods:
        print(x.find("name").string, ": ", x.price.get_text())


# example()


def test1():
    # Đọc file XML
    with open("complex_menu.xml", "r", encoding="utf-8") as file:
        content = file.read()
    # Khởi tạo đối tượng BeautifulSoup
    soup = BeautifulSoup(content, "lxml-xml")

    # Tìm và in tất cả các mục food
    foods = soup.find_all("food")
    for food in foods:
        food_type = food["type"]
        # food_type = food.get_attribute_list("type")
        name = food.find("name").string
        price = food.find("price").string
        currency = food.find("price")["currency"]
        description = food.find("description").string
        calories = food.find("calories").string
        ingredients = [
            ingredient.string
            for ingredient in food.find("ingredients").find_all("ingredient")
        ]

        print(f"Type: {food_type}")
        print(f"Name: {name}")
        print(f"Price: {price} ({currency})")
        print(f"Description: {description}")
        print(f"Calories: {calories}")
        print(f"Ingredients: {', '.join(ingredients)}")
        print()


# test1()


def test2():
    xml_data = """<?xml version="1.0" encoding="UTF-8"?>
<restaurant>
    <menu>
        <item category="main">
            <name>Grilled Salmon</name>
            <price>15.95</price>
            <description>Grilled salmon with <highlight>fresh herbs</highlight></description>
            <nutrition>
                <calories>600</calories>
                <protein>50g</protein>
                <fat>30g</fat>
            </nutrition>
        </item>
    </menu>
</restaurant>"""

    soup = BeautifulSoup(xml_data, "xml")

    item = soup.find("item")
    description_string = item.find("description").string  # None

    # "Grilled salmon with fresh herbs"
    description_text = item.find("description").text

    # ['Grilled salmon with ', <highlight>fresh herbs</highlight>]
    description_contents = item.find("description").contents

    # tag lồng
    description_get_text = item.find("description").get_text(
        separator=" | ", strip=True
    )

    category = item.get("category")  # "main"
    print(f"string: {description_string}")
    print(f"text: {description_text}")
    print(f"contents: {description_contents}")
    print(f"get_text: {description_get_text}")
    print(f"category attribute: {category}")


# test2()
