import streamlit as st
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import time

min_date = datetime(1990, 1, 1)
max_date = datetime.now()
# st.write({"key": "value"})
# st.write(True)
# st.write(123)
# 3 + 4
# "xin chao the gioi " if True else "bye"
# "flase" if False else "true"

# #########################################################################################

# press1 = st.button("click me")
# print("first: ", press1)

# press2 = st.button("second button")
# print("second: ", press2)
# #########################################################################################

# st.title("title")
# st.header("header")
# st.subheader("subheader")
# st.markdown("thi si **markdown**")
# st.markdown("thi si __markdown__")
# st.markdown("thi si _markdown_")
# st.caption("small text")

# code_example = """def greet(name):
#     print('hello',name)"""
# st.code(code_example, language="python")

# st.divider()
# # st.image(os.path.join(os.getcwd(), "as.png"))
# st.image(os.path.join(os.getcwd(), "web_crapping", "as.png"), width=150)
# #########################################################################################


# st.subheader("Dataframe")
# df = pd.DataFrame(
#     {
#         "Name": ["Alice", "Bob", "Charlie", "David"],
#         "Age": [25, 32, 37, 45],
#         "Occupation": ["Engineer", "Doctor", "Artist", "Chef"],
#     }
# )

# st.dataframe(df)

# # Data Editor Section (Editable dataframe)
# st.subheader("Data Editor")
# editable_df = st.data_editor(df)
# print(editable_df)
# # Static Table Section
# st.subheader("Static Table")
# st.table(df)

# st.subheader("metrics")
# st.metric(label="total rows", value=len(df))
# st.metric(label="avg age", value=round(df["Age"].mean(), 1))

# st.subheader("json and dictionnary")
# sample_dict = {
#     "name": "alice",
#     "age": 25,
#     "skill": ["python", "data analysis", "machine learning"],
# }
# st.json(sample_dict)

# st.write('dictionary view: ',sample_dict)
# #########################################################################################
# Title
# st.title("Streamlit Charts Demo")
# # Generate sample data
# chart_data = pd.DataFrame(np.random.randn(20, 3), columns=["A", "B", "C"])

# # Area Chart Section
# st.subheader("Area Chart")
# st.area_chart(chart_data)

# # Bar Chart Section
# st.subheader("Bar Chart")
# st.bar_chart(chart_data)

# # Line Chart Section
# st.subheader("Line Chart")
# st.line_chart(chart_data)

# # Scatter Chart Section
# st.subheader("Scatter Chart")
# scatter_data = pd.DataFrame({"x": np.random.randn(100), "y": np.random.randn(100)})
# st.scatter_chart(scatter_data)


# # Map section ( display random point on map)
# st.subheader("Map")
# map_data = pd.DataFrame(
#     np.random.randn(100, 2) / [50, 50] + [37.76, -122.4],  # Coordinates around SF
#     columns=["lat", "lon"],
# )
# st.map(map_data)


# # Pyplot Section
# st.subheader("Pyplot Chart")
# fig, ax = plt.subplots()
# ax.plot(chart_data["A"], label="A")
# ax.plot(chart_data["B"], label="B")
# ax.plot(chart_data["C"], label="C")
# ax.set_title("Pyplot Line Chart")
# ax.legend()
# st.pyplot(fig)
# #########################################################################################

# st.title("Streamlit Form Demo")
# # Form to hold the interactive elements
# with st.form(key="sample_form"):
#     # Text Input
#     st.subheader("Text Inputs")
#     name = st.text_input("Enter your name")
#     feedback = st.text_area("Provide your feedback")

#     # Date and Time Inputs
#     st.subheader("Date and Time Inputs")
#     dob = st.date_input("Select your date of birth")
#     time = st.time_input("Choose a preferred time")

#     # Selectors
#     st.subheader("Selectors")
#     choice = st.radio("Choose an option", ["Option 1", "Option 2", "Option 3"])
#     gender = st.selectbox("Select your gender", ["Male", "Female", "Other"])
#     slider_value = st.select_slider("Select a range", options=[1, 2, 3, 4, 5])

#     # Toggles and Checkboxes
#     st.subheader("Toggles & Checkboxes")
#     notifications = st.checkbox("Receive notifications?")
#     toggle_value = st.checkbox("Enable dark mode?", value=False)

#     submit_button = st.form_submit_button(label="submit")

# # Display the entered information if the form is submitted
# st.header("Buttons")
# if submit_button:
#     st.write("### Entered Information")
#     st.write("**Name:**", name)
#     st.write("**Feedback:**", feedback)
#     st.write("**Date of Birth:**", dob)
#     st.write("**Preferred Time:**", time)
#     st.write("**Selected Option:**", choice)
#     st.write("**Gender:**", gender)
#     st.write("**Slider Value:**", slider_value)
#     st.write("**Notifications Enabled:**", notifications)
#     st.write("**Dark Mode Enabled:**", toggle_value)
#     download_data = f"Name: {name}\nFeedback: {feedback}\nDate of Birth: {dob}\nPreferred Time: {time}"
#     st.download_button(label="Download Info", data=download_data, file_name="user_info.txt", mime="text/plain")
#     st.markdown('[Visit Google](https://www.google.com)', unsafe_allow_html=True)
# #########################################################################################

# st.title("user information")

# form_value = {
#     "name": None,
#     "height": None,
#     "gender": None,
#     "dob": None,
# }
# with st.form(key="user_infor_form"):
#     form_value["name"] = st.text_input("enter ur name")
#     form_value["height"] = st.number_input("Enter ur height (cm)")
#     form_value["gender"] = st.selectbox("Enter ur gender",['male','female'])
#     form_value["dob"] = st.date_input("Enter ur birthdate",max_value=max_date,min_value=min_date)


#     submit_button= st.form_submit_button('done')
#     print('after submit')
#     if submit_button:
#         if not all(form_value.values()):
#             st.warning('Please fill  all of fields')
#         else:
#             st.balloons()
#             st.write('### info')
#             for (key,value) in form_value.items():
#                 st.write(f'{key}: {value}')
# #########################################################################################

# st.title("User Information Form")
# with st.form(key="user_info_form", clear_on_submit=True):
#     name1 = st.text_input("Enter your first name")
#     birth_date = st.date_input(
#         "Enter your birth date", min_value=min_date, max_value=max_date
#     )
#     if birth_date:
#         age = max_date.year - birth_date.year
#     if birth_date.month > max_date.month or (
#         birth_date.month == max_date.month and birth_date.day > max_date.day
#     ):
#         age -= 1
#     st.write(f"Your calculated age is {age} years")

#     submit_button1 = st.form_submit_button(label="Submit Form")

#     if submit_button1:
#         if not name1 or not birth_date:
#             st.warning("Please fill in all form inputs")
#         else:
#             st.success(f"Thank you, {name1}. Your age is {age}.")
#             st.balloons()
# #########################################################################################
# session state
# if "counter" not in st.session_state:
#     st.session_state.counter = 0

# if st.button("Increment Counter"):
#     st.session_state.counter += 1
#     st.write(f"Counter incremented to {st.session_state.counter}")

# if st.button("Reset"):
#     st.session_state.counter = 0

# st.write(f"Counter value: {st.session_state.counter}")
# #########################################################################################
# callback
# if "step" not in st.session_state:
#     st.session_state.step = 1

# if "info" not in st.session_state:
#     st.session_state.info = {}


# def go_step2(name):
#     st.session_state.info["name"] = name  # Save the name to session state
#     st.session_state.step = 2  # Move to the next step


# def go_step1():
#     st.session_state.step = 1


# if st.session_state.step == 1:
#     st.header("Part 1: Info")
#     name = st.text_input("Name", value=st.session_state.info.get("name", ""))
#     st.button("Next", on_click=go_step2, args=(name,))


# if st.session_state.step == 2:
#     st.header("Part 2: Review")
#     st.write("Please review:")
#     st.write(f"**Name**: {st.session_state.info.get('name', '')}")

#     if st.button("Submit"):
#         st.success("Great!")
#         st.balloons()
#         st.session_state.info = {}  # Clear session state info after submission

#     # Back button to return to step 1
#     st.button("Back", on_click=go_step1)
# #########################################################################################

# callback : sidebar,column,tabs,cointainer,placeholder,expander
# st.sidebar.title("This is the Sidebar")
# st.sidebar.write("You can place elements like sliders, buttons, and text here.")
# sidebar_input = st.sidebar.text_input("Enter something in the sidebar")
# # Tabs layout
# tab1, tab2, tab3 = st.tabs(["Tab 1", "Tab 2", "Tab 3"])
# with tab1:
#     st.write("You are in Tab 1")
# with tab2:
#     st.write("You are in Tab 2")
# with tab3:
#     st.write("You are in Tab 3")
# # Columns layout
# col1, col2,col3 = st.columns(3)
# with col1:
#     st.header("Column 1")
#     st.write("Content for column 1")
# with col2:
#     st.header("Column 2")
#     st.write("Content for column 2")
# with col3:
#     st.header("Column 3")
#     st.write("Content for column 3 hehe")

# # Containers example
# with st.container (border=True):
#     st.write("This is inside a container.")
#     st.write("You can think of containers as a grouping for elements.")
#     st.write("Containers help manage sections of the page.")

#     # Empty placeholder
# placeholder = st.empty()
# placeholder.write("This is an empty placeholder, useful for dynamic content.")


# if st.button("Update Placeholder"):
#     placeholder.write('the conten update')
# with st.expander('expand for more detail'):
#     st.write('addition infor')
#     st.write('can use enpanders to keep interface clenaer')


# #Popover (Tooltip)
# st.write("Hover over this button for a tooltip")
# st.button("Button with Tooltip", help="This is a tooltip or popover on hover.")
# #Sidebar input handling
# if sidebar_input:
#     st.write(f"You entered in the sidebar: {sidebar_input}")
# # #########################################################################################
# widget
# st.button("OK", key="btn1")
# st.button("OK", key="btn2")
# min_vl = st.slider("set minimum value", 0, 50, 25)
# slider_vl = st.slider("slider", min_vl, 100, min_vl)
###################
# if "slider" not in st.session_state:
#     st.session_state.slider = 25
# min_vl = st.slider("set minimum value", 0, 50, 25)
# st.session_state.slider = st.slider("slider", min_vl, 100, st.session_state.slider)
# ###################
# if "checkbox" not in st.session_state:
#     st.session_state.checkbox = False


# def toggle_input():
#     st.session_state.checkbox = not st.session_state.checkbox


# st.checkbox("Show Input Field", value=st.session_state.checkbox, on_change=toggle_input)


# if st.session_state.checkbox:
#     user_input = st.text_input("Enter something:")
#     st.session_state.user_input = user_input
# else:
#     user_input = st.session_state.get("user_input", "")
# st.write(f"User Input: {user_input}")
# # #########################################################################################
# caching
# @st.cache_data(ttl=60)  # 60s
# def fetch_data():
#     time.sleep(3)
#     return {"data": "this is a data"}


# st.write("fetching data")
# data = fetch_data()
# st.write(data)
# #########################################################################################

# file_path = "example.txt"
# @st.cache_resource
# def get_file_handler():
#     # Open the file in append mode, which creates the file if it doesn't
#     file = open(file_path, "a+")
#     return file


# # Use the cached file handler
# file_handler = get_file_handler()
# # Write to the file using the cached handler
# if st.button("Write to File"):
#     file_handler.write("New line of text\n")
#     file_handler.flush()  # Ensure the content is written immediately
#     st.success("ok")
# if st.button("read from File"):
#     file_handler.seek(0)
#     content = file_handler.read()
#     st.text(content)

# st.button("close file", on_click=file_handler.close)
########################################################################################
# st.retun()
# st.title("Counter Example with Immediate Rerun")
# if "count" not in st.session_state:
#     st.session_state.count = 0


# def increment_and_rerun():
#     st.session_state.count += 1
#     st.rerun()  # tải lại (rerun) toàn bộ ứng dụng ngay sau khi hàm chạy xong.


# st.write(f"Current Count: {st.session_state.count}")
# if st.button("Increment and Update Immediately"):
#     increment_and_rerun()
#####################################################################################
# # fragment
# st.title("Fragment")


# @st.fragment()
# def toggle_and_text():
#     cols = st.columns(2)
#     cols[0].toggle("Toggle")
#     cols[1].text_area("Enter text toggle")
#     # st.rerun(scope="fragment")


# @st.fragment()
# def filter_and_file():
#     new_cols = st.columns(5)
#     new_cols[0].checkbox("Filter")
#     new_cols[1].file_uploader("Upload image")
#     new_cols[2].selectbox("Choose option", ["Option 1", "Option 2", "Option 3"])
#     new_cols[3].slider("Select value", 0, 100, 50)
#     new_cols[4].text_input("Enter text")


# toggle_and_text()
# cols = st.columns(2)
# cols[0].selectbox("Select", [1, 2, 3], None)
# cols[1].button("Update")
# filter_and_file()
######################################################################################
# multi page app
st.title("home")


# Define page funct
def intro():
    st.title("Welcome to My App")
    st.write(
        " This is the introduction page. Use the dropdown to navigate to different demos."
    )


def plotting_demo():
    st.title("Plotting Demo")
    st.write("Here, we create a simple plot.")
    # Example plot with random data
    chart_data = pd.DataFrame(np.random.randn(50, 3), columns=["a", "b", "c"])
    st.line_chart(chart_data)


def mapping_demo():
    st.title("Mapping Demo")
    st.write("This page shows a map with random points.")
    # Generate some random geospatial data
    map_data = pd.DataFrame(
        np.random.randn(1000, 2) / [50, 50] + [37.76, -122.4], columns=["lat", "lon"]
    )
    st.map(map_data)


def data_frame_demo():
    st.title("DataFrame Demo")
    st.write("Here we display a sample data frame.")
    df = pd.DataFrame(np.random.randn(20, 3), columns=["column1", "column2", "column3"])
    st.dataframe(df)


page_name_to_function = {
    "-": intro,
    "plotting_demo": plotting_demo,
    "mapping_demo": mapping_demo,
    "data_frame_demo": data_frame_demo,
}
select_page = st.sidebar.selectbox("select apge", options=page_name_to_function.keys())
page_name_to_function[select_page]()
